#include "Common.h"
#include "slang/ast/Symbol.h"
#include "slang/syntax/SyntaxNode.h"
#include "slang/text/SourceLocation.h"

namespace syntax_symbol_mapper {

std::string mapperErrorToString(MapperError error) {
    switch (error) {
        case MapperError::Success:
            return "Success";
        case MapperError::InvalidInput:
            return "Invalid input parameters";
        case MapperError::CompilationFailed:
            return "Compilation failed";
        case MapperError::SymbolNotFound:
            return "Symbol not found";
        case MapperError::SyntaxNotFound:
            return "Syntax node not found";
        case MapperError::CacheOverflow:
            return "Cache overflow";
        case MapperError::InternalError:
            return "Internal error";
        default:
            return "Unknown error";
    }
}

std::string getSymbolKindString(const Symbol* symbol) {
    if (!symbol) {
        return "null";
    }

    // Convert SymbolKind enum to string
    // This is a simplified version - in practice you'd want to include
    // the full slang headers and use their enum-to-string utilities
    switch (static_cast<int>(symbol->kind)) {
        case 0: return "Unknown";
        case 1: return "Root";
        case 2: return "Definition";
        case 3: return "CompilationUnit";
        case 4: return "DeferredMember";
        case 5: return "TransparentMember";
        case 6: return "EmptyMember";
        // Add more cases as needed
        default: return "SymbolKind(" + std::to_string(static_cast<int>(symbol->kind)) + ")";
    }
}

std::string getSyntaxKindString(const SyntaxNode* syntax) {
    if (!syntax) {
        return "null";
    }

    // Convert SyntaxKind enum to string
    // This is a simplified version - in practice you'd want to include
    // the full slang headers and use their enum-to-string utilities
    switch (static_cast<int>(syntax->kind)) {
        case 0: return "Unknown";
        case 1: return "List";
        case 2: return "SeparatedList";
        // Add more cases as needed
        default: return "SyntaxKind(" + std::to_string(static_cast<int>(syntax->kind)) + ")";
    }
}

std::string getLocationString(const SyntaxNode* syntax) {
    if (!syntax) {
        return "unknown";
    }

    // Get the first token's location
    auto token = syntax->getFirstToken();
    if (!token) {
        return "unknown";
    }

    auto loc = token.location();
    if (!loc.valid()) {
        return "unknown";
    }

    // For now, just return the offset since we don't have access to SourceManager here
    return "offset:" + std::to_string(loc.offset());
}

} // namespace syntax_symbol_mapper
