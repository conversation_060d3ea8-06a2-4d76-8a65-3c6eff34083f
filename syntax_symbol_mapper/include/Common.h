#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <optional>
#include <functional>

// Forward declarations for slang types
namespace slang::syntax {
    class SyntaxNode;
    class SyntaxTree;
}

namespace slang::ast {
    class Symbol;
    class Compilation;
    class Scope;
}

namespace syntax_symbol_mapper {

// Type aliases for convenience
using SyntaxNode = slang::syntax::SyntaxNode;
using SyntaxTree = slang::syntax::SyntaxTree;
using Symbol = slang::ast::Symbol;
using Compilation = slang::ast::Compilation;
using Scope = slang::ast::Scope;

// Mapping types
using SyntaxToSymbolMap = std::unordered_map<const SyntaxNode*, const Symbol*>;
using SymbolToSyntaxMap = std::unordered_map<const Symbol*, const SyntaxNode*>;

// Result types
struct MappingResult {
    bool success;
    std::string error_message;
    size_t mapped_count;
    
    MappingResult(bool success = true, const std::string& error = "", size_t count = 0)
        : success(success), error_message(error), mapped_count(count) {}
};

struct SymbolInfo {
    const Symbol* symbol;
    const SyntaxNode* syntax;
    std::string name;
    std::string kind;
    std::string location;
    
    SymbolInfo(const Symbol* sym = nullptr, const SyntaxNode* syn = nullptr)
        : symbol(sym), syntax(syn) {}
};

// Configuration options
struct MapperConfig {
    bool enable_caching = true;
    bool deep_traversal = true;
    bool include_implicit_symbols = false;
    size_t max_cache_size = 10000;
    
    MapperConfig() = default;
};

// Error codes
enum class MapperError {
    Success = 0,
    InvalidInput,
    CompilationFailed,
    SymbolNotFound,
    SyntaxNotFound,
    CacheOverflow,
    InternalError
};

// Utility functions
std::string mapperErrorToString(MapperError error);
std::string getSymbolKindString(const Symbol* symbol);
std::string getSyntaxKindString(const SyntaxNode* syntax);
std::string getLocationString(const SyntaxNode* syntax);

} // namespace syntax_symbol_mapper
