
import pyslang

class SymbolMappingVisitor:
    """
    A visitor that traverses a SyntaxTree and maps every identifier
    node to its corresponding semantic Symbol using the user's
    upward-traversal resolution algorithm.
    """
    def __init__(self, compilation):
        self.compilation = compilation
        self.root_scope = compilation.getRoot()
        self.mapping = {}

    def map_symbols(self, tree):
        """
        Starts the traversal of the tree to populate the symbol map.
        """
        self.visit(tree.root)
        return self.mapping

    def visit(self, node):
        """
        The main visitor method. It resolves the current node if it's
        an identifier, then recursively visits all children.
        """
        if node is None:
            return

        # 1. If the node is an identifier, resolve it.
        if isinstance(node, pyslang.IdentifierNameSyntax):
            resolved_symbol = self._resolve_symbol(node)
            if resolved_symbol:
                self.mapping[node] = resolved_symbol

        # 2. Recursively visit all children (if it's a node and not a token).
        if isinstance(node, pyslang.SyntaxNode):
            for child in node:
                self.visit(child)

    def _resolve_symbol(self, node_to_resolve):
        """
        Implements the upward-scoping search to find the symbol for a
        given identifier node.
        """
        identifier_name = node_to_resolve.identifier.valueText
        current_parent = node_to_resolve.parent

        while current_parent:
            # Check if the current parent node corresponds to a semantic scope.
            parent_symbol = self._get_symbol_for_syntax(current_parent)
            if parent_symbol and isinstance(parent_symbol, pyslang.Scope):
                # We found a semantic scope. Look up the identifier within it.
                found_symbol = parent_symbol.lookupName(identifier_name)
                if found_symbol:
                    return found_symbol
            
            current_parent = current_parent.parent
        
        return None

    def _get_symbol_for_syntax(self, syntax_node):
        """
        Finds the semantic Symbol for a scope-defining SyntaxNode.
        This is the bridge from a syntactic scope to a semantic one.
        """
        if isinstance(syntax_node, pyslang.ModuleDeclarationSyntax):
            # For a module, we can look it up by name from the root scope.
            return self.root_scope.lookupName(syntax_node.name.valueText)
        # This could be extended for functions, classes, etc.
        return None

# --- Main ---

source_code = """
module my_mod;
    int i; // The DECLARATION
    always @*
        i <= 1; // The USAGE
endmodule
"""

# 1. Compile for semantics
tree = pyslang.SyntaxTree.fromText(source_code)
compilation = pyslang.Compilation()
compilation.addSyntaxTree(tree)
list(compilation.getAllDiagnostics()) # Force semantic analysis

# 2. Use the visitor to build the map
visitor = SymbolMappingVisitor(compilation)
symbol_map = visitor.map_symbols(tree)

# 3. Verification
print("--- Verifying Symbol Mapping ---")
# Manually find the usage node in the tree to check the map
usage_node = tree.root.members[0].members[1].statement.expr.left

# Find the corresponding symbol in our map
usage_symbol = symbol_map.get(usage_node)

# Get the declaration symbol directly for verification
declaration_symbol = compilation.getRoot().lookupName("my_mod.i")

if usage_symbol and declaration_symbol:
    print(f"Symbol for Declaration ('i'): '{declaration_symbol.name}' at {declaration_symbol.location}")
    print(f"Symbol resolved from Usage ('i'): '{usage_symbol.name}' at {usage_symbol.location}")
    print("-" * 40)

    are_same_object = declaration_symbol is usage_symbol
    print(f"Are they the same symbol object? -> {are_same_object}")
    assert are_same_object
    print("Success! The visitor correctly mapped the usage node to its canonical Symbol.")
else:
    print("Verification failed.")
    print(f"Usage symbol found in map: {usage_symbol is not None}")
    print(f"Declaration symbol found directly: {declaration_symbol is not None}")

