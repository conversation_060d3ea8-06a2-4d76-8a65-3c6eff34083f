

import pyslang

class MyDiagnosticClient(pyslang.DiagnosticClient):
    def __init__(self):
        super().__init__()
        self.diagnostics = []

    def report(self, diag):
        self.diagnostics.append(diag)

def find_node_at_offset(node, offset):
    """
    Recursively finds the most specific syntax node at a given character offset.
    """
    if isinstance(node, pyslang.Token):
        return node

    for i in range(len(node)):
        child = node[i]
        if hasattr(child, 'sourceRange'):
            child_range = child.sourceRange
            if child_range.start.offset <= offset and offset < child_range.end.offset:
                return find_node_at_offset(child, offset)
    return node

# --- Main ---

# 1. Define the SystemVerilog source code
source = """
module my_mod;
    int i; // This is the DECLARATION of 'i'

    always @* begin
        i = 1; // This is a USAGE of 'i'
    end
endmodule
"""

# 2. Parse the code to get the syntax tree
tree = pyslang.SyntaxTree.fromText(source)

# 3. Perform a compilation to build the full semantic model
compilation = pyslang.Compilation()
compilation.addSyntaxTree(tree)

# 4. Create a diagnostic engine and client
engine = pyslang.DiagnosticEngine(compilation.sourceManager)
client = MyDiagnosticClient()
engine.addClient(client)

# 5. Force analysis by issuing diagnostics
for diag in compilation.getAllDiagnostics():
    engine.issue(diag)

# 6. Locate the target SyntaxNode for the USAGE of 'i'
usage_offset = source.find("i = 1")
usage_token = find_node_at_offset(tree.root, usage_offset)
usage_node = usage_token.parent # The parent is the IdentifierNameSyntax

print(f"Found usage SyntaxNode: '{usage_node}' of kind {usage_node.kind}")
print("-" * 20)

# 7. Find the symbol by looking for a diagnostic at the usage location
resolved_symbol = None
for diag in client.diagnostics:
    if diag.location == usage_node.location:
        # This is a diagnostic for the node we are interested in.
        # The symbol is in the diagnostic's arguments.
        for arg in diag.args:
            if isinstance(arg, pyslang.Symbol):
                resolved_symbol = arg
                break
    if resolved_symbol:
        break

# --- Verification ---
if resolved_symbol:
    print("Successfully mapped SyntaxNode to Symbol!")
    print(f"Resolved Symbol Name: '{resolved_symbol.name}'")
    print(f"Resolved Symbol Kind: {resolved_symbol.kind}")

    declaration_loc = resolved_symbol.location
    print(f"Symbol's Declaration Location: {declaration_loc}")

    declaration_offset = source.find("int i;")
    print(f"Actual declaration offset in source: {declaration_offset}")
    print(f"Offset found from symbol: {declaration_loc.offset}")

    assert declaration_loc.offset == declaration_offset
    print("\nValidation successful: The usage node correctly mapped to its declaration symbol.")
else:
    print("Failed to map the syntax node to a symbol.")
