#include "SyntaxToSymbolMapper.h"
#include "SymbolResolver.h"
#include "MappingCache.h"

#include "slang/ast/Compilation.h"
#include "slang/ast/Symbol.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/syntax/SyntaxNode.h"
#include "slang/syntax/SyntaxVisitor.h"

namespace syntax_symbol_mapper {

class SyntaxToSymbolMapper::Impl {
public:
    explicit Impl(Compilation& comp, const MapperConfig& cfg)
        : compilation(comp), config(cfg), resolver(comp),
          cache(cfg.enable_caching ? std::make_unique<MappingCache>(cfg.max_cache_size) : nullptr) {
    }

    MappingResult buildMapping(const SyntaxTree& tree) {
        try {
            clear();

            // Create a visitor to traverse the syntax tree
            MappingVisitor visitor(*this);
            visitor.visit(tree.root());

            return MappingResult(true, "", syntaxToSymbol.size());
        }
        catch (const std::exception& e) {
            return MappingResult(false, e.what(), 0);
        }
    }

    const Symbol* findSymbol(const SyntaxNode* syntax) const {
        if (!syntax) return nullptr;

        // Check cache first
        if (cache) {
            auto cached = cache->findSymbol(syntax);
            if (cached) return cached;
        }

        // Check direct mapping
        auto it = syntaxToSymbol.find(syntax);
        if (it != syntaxToSymbol.end()) {
            if (cache) {
                cache->addMapping(syntax, it->second);
            }
            return it->second;
        }

        return nullptr;
    }

    const SyntaxNode* findSyntax(const Symbol* symbol) const {
        if (!symbol) return nullptr;

        // Check cache first
        if (cache) {
            auto cached = cache->findSyntax(symbol);
            if (cached) return cached;
        }

        // Check direct mapping
        auto it = symbolToSyntax.find(symbol);
        if (it != symbolToSyntax.end()) {
            if (cache) {
                cache->addMapping(it->second, symbol);
            }
            return it->second;
        }

        return nullptr;
    }

    SymbolInfo getSymbolInfo(const SyntaxNode* syntax) const {
        SymbolInfo info(findSymbol(syntax), syntax);

        if (info.symbol) {
            info.name = std::string(info.symbol->name);
            info.kind = getSymbolKindString(info.symbol);
        }

        if (info.syntax) {
            info.location = getLocationString(info.syntax);
        }

        return info;
    }

    void clear() {
        syntaxToSymbol.clear();
        symbolToSyntax.clear();
        if (cache) {
            cache->clear();
        }
    }

    void addMapping(const SyntaxNode* syntax, const Symbol* symbol) {
        if (!syntax || !symbol) return;

        syntaxToSymbol[syntax] = symbol;
        symbolToSyntax[symbol] = syntax;

        if (cache) {
            cache->addMapping(syntax, symbol);
        }
    }

    const SyntaxToSymbolMap& getSyntaxToSymbolMap() const {
        return syntaxToSymbol;
    }

    const SymbolToSyntaxMap& getSymbolToSyntaxMap() const {
        return symbolToSyntax;
    }

    void updateConfig(const MapperConfig& newConfig) {
        config = newConfig;
    }

    const MapperConfig& getConfig() const {
        return config;
    }

private:
    class MappingVisitor {
    public:
        explicit MappingVisitor(Impl& impl) : mapperImpl(impl) {}

        void visit(const SyntaxNode& node) {
            // Try to resolve symbol for this node
            auto symbol = mapperImpl.resolver.resolveSymbol(&node);
            if (symbol) {
                mapperImpl.addMapping(&node, symbol);
            }

            // Also check if this node created a symbol (reverse mapping)
            auto createdSymbol = mapperImpl.resolver.findSymbolFromSyntax(&node);
            if (createdSymbol) {
                mapperImpl.addMapping(&node, createdSymbol);
            }

            // Visit children if deep traversal is enabled
            if (mapperImpl.config.deep_traversal) {
                for (size_t i = 0; i < node.getChildCount(); i++) {
                    auto child = node.childNode(i);
                    if (child) {
                        visit(*child);
                    }
                }
            }
        }

    private:
        Impl& mapperImpl;
    };

    Compilation& compilation;
    MapperConfig config;
    SymbolResolver resolver;
    std::unique_ptr<MappingCache> cache;

    SyntaxToSymbolMap syntaxToSymbol;
    SymbolToSyntaxMap symbolToSyntax;
};

// Implementation of public interface
SyntaxToSymbolMapper::SyntaxToSymbolMapper(Compilation& compilation, const MapperConfig& config)
    : pImpl(std::make_unique<Impl>(compilation, config)) {
}

SyntaxToSymbolMapper::~SyntaxToSymbolMapper() = default;

SyntaxToSymbolMapper::SyntaxToSymbolMapper(SyntaxToSymbolMapper&&) noexcept = default;
SyntaxToSymbolMapper& SyntaxToSymbolMapper::operator=(SyntaxToSymbolMapper&&) noexcept = default;

MappingResult SyntaxToSymbolMapper::buildMapping(const SyntaxTree& tree) {
    return pImpl->buildMapping(tree);
}

const Symbol* SyntaxToSymbolMapper::findSymbol(const SyntaxNode* syntax) const {
    return pImpl->findSymbol(syntax);
}

const SyntaxNode* SyntaxToSymbolMapper::findSyntax(const Symbol* symbol) const {
    return pImpl->findSyntax(symbol);
}

SymbolInfo SyntaxToSymbolMapper::getSymbolInfo(const SyntaxNode* syntax) const {
    return pImpl->getSymbolInfo(syntax);
}

const SyntaxToSymbolMap& SyntaxToSymbolMapper::getSyntaxToSymbolMap() const {
    return pImpl->getSyntaxToSymbolMap();
}

const SymbolToSyntaxMap& SyntaxToSymbolMapper::getSymbolToSyntaxMap() const {
    return pImpl->getSymbolToSyntaxMap();
}

void SyntaxToSymbolMapper::clear() {
    pImpl->clear();
}

size_t SyntaxToSymbolMapper::getMappingCount() const {
    return pImpl->getSyntaxToSymbolMap().size();
}

bool SyntaxToSymbolMapper::isMapped(const SyntaxNode* syntax) const {
    return findSymbol(syntax) != nullptr;
}

void SyntaxToSymbolMapper::updateConfig(const MapperConfig& config) {
    pImpl->updateConfig(config);
}

const MapperConfig& SyntaxToSymbolMapper::getConfig() const {
    return pImpl->getConfig();
}

} // namespace syntax_symbol_mapper
