#!/usr/bin/env python3
"""
Test runner for SystemVerilog LSP.

This script provides various options for running tests with different
configurations and reporting options.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, cwd=None):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="SystemVerilog LSP Test Runner")
    parser.add_argument(
        "--category",
        choices=["unit", "integration", "analysis", "lsp", "performance", "utils", "all"],
        default="all",
        help="Test category to run"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Run with coverage reporting"
    )
    parser.add_argument(
        "--html-report",
        action="store_true",
        help="Generate HTML coverage report"
    )
    parser.add_argument(
        "--benchmark",
        action="store_true",
        help="Run performance benchmarks"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run only quick tests (skip slow integration tests)"
    )
    parser.add_argument(
        '--no-capture',
        action="store_true",
        help="Don't capture stdout and stderr"
    )

    args = parser.parse_args()

    # Set up paths
    project_root = Path(__file__).parent
    tests_dir = project_root / "tests"

    # Set PYTHONPATH
    env_setup = f"PYTHONPATH={project_root}/src"

    print("🧪 SystemVerilog LSP Test Runner")
    print("=" * 50)

    # Build pytest command
    pytest_cmd = [env_setup, "python", "-m", "pytest"]

    # Add test directory based on category
    if args.category == "all":
        pytest_cmd.append(str(tests_dir))
    else:
        category_dir = tests_dir / args.category
        if category_dir.exists():
            pytest_cmd.append(str(category_dir))
        else:
            print(f"❌ Test category '{args.category}' not found at {category_dir}")
            return 1

    # Add pytest options
    if args.verbose:
        pytest_cmd.extend(["-v", "-s"])

    if args.quick:
        pytest_cmd.extend(["-m", "not slow"])

    if args.coverage:
        pytest_cmd.extend([
            "--cov=sv_lsp",
            "--cov-report=term-missing",
            f"--cov-config={project_root}/.coveragerc"
        ])

        if args.html_report:
            pytest_cmd.extend([
                "--cov-report=html:htmlcov"
            ])

    if args.benchmark:
        pytest_cmd.extend([
            "--benchmark-only",
            "--benchmark-sort=mean"
        ])

    # Add common pytest options
    pytest_cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--disable-warnings"
    ])

    if args.no_capture:
        pytest_cmd.extend(["--capture=no"])

    # Run the tests
    cmd_str = " ".join(pytest_cmd)
    print(f"🚀 Running: {cmd_str}")
    print()

    success, stdout, stderr = run_command(cmd_str, cwd=project_root)

    # Print output
    if stdout:
        print(stdout)
    if stderr:
        print(stderr, file=sys.stderr)

    # Summary
    if success:
        print("\n✅ Tests completed successfully!")

        if args.coverage and args.html_report:
            print(f"📊 Coverage report generated: {project_root}/htmlcov/index.html")

        return 0
    else:
        print("\n❌ Tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
