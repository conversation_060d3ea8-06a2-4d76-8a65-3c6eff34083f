#pragma once

#include "Common.h"
#include <unordered_set>

namespace syntax_symbol_mapper {

/**
 * @brief Cache for storing and managing syntax-symbol mappings
 * 
 * This class provides efficient caching mechanisms for symbol mappings
 * with LRU eviction and performance optimization.
 */
class MappingCache {
public:
    /**
     * @brief Constructor
     * @param max_size Maximum number of entries to cache
     */
    explicit MappingCache(size_t max_size = 10000);
    
    /**
     * @brief Destructor
     */
    ~MappingCache();
    
    /**
     * @brief Add a syntax-symbol mapping to the cache
     * @param syntax The syntax node
     * @param symbol The corresponding symbol
     */
    void addMapping(const SyntaxNode* syntax, const Symbol* symbol);
    
    /**
     * @brief Find symbol for a syntax node in cache
     * @param syntax The syntax node to look up
     * @return Pointer to the symbol, or nullptr if not cached
     */
    const Symbol* findSymbol(const SyntaxNode* syntax) const;
    
    /**
     * @brief Find syntax node for a symbol in cache
     * @param symbol The symbol to look up
     * @return Pointer to the syntax node, or nullptr if not cached
     */
    const SyntaxNode* findSyntax(const Symbol* symbol) const;
    
    /**
     * @brief Check if a syntax node is cached
     * @param syntax The syntax node to check
     * @return True if the node is in cache
     */
    bool contains(const SyntaxNode* syntax) const;
    
    /**
     * @brief Check if a symbol is cached
     * @param symbol The symbol to check
     * @return True if the symbol is in cache
     */
    bool contains(const Symbol* symbol) const;
    
    /**
     * @brief Clear all cached mappings
     */
    void clear();
    
    /**
     * @brief Get current cache size
     * @return Number of cached entries
     */
    size_t size() const;
    
    /**
     * @brief Get maximum cache size
     * @return Maximum number of entries
     */
    size_t maxSize() const;
    
    /**
     * @brief Set maximum cache size
     * @param max_size New maximum size
     */
    void setMaxSize(size_t max_size);
    
    /**
     * @brief Get cache hit rate
     * @return Hit rate as a percentage (0.0 to 1.0)
     */
    double getHitRate() const;
    
    /**
     * @brief Get cache statistics
     * @return Statistics about cache performance
     */
    struct CacheStats {
        size_t hits;
        size_t misses;
        size_t evictions;
        size_t current_size;
        size_t max_size;
        double hit_rate;
    };
    
    CacheStats getStats() const;
    
    /**
     * @brief Reset cache statistics
     */
    void resetStats();

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace syntax_symbol_mapper
