"""
Test helpers and utilities for SystemVerilog LSP tests.

This module provides common utilities and setup functions for all tests.
"""

import sys
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

# Ensure src is in Python path for all tests
_PROJECT_ROOT = Path(__file__).parent.parent
_SRC_PATH = _PROJECT_ROOT / "src"

if str(_SRC_PATH) not in sys.path:
    sys.path.insert(0, str(_SRC_PATH))

# Now we can safely import our modules
try:
    from sv_lsp.core.types import SymbolKind, Location, SymbolInfo
    from sv_lsp.analysis.symbol_analyzer import SymbolAnalyzer
    from sv_lsp.core.semantic_model import SemanticModel
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import sv_lsp modules: {e}")
    IMPORTS_AVAILABLE = False


def ensure_imports() -> bool:
    """
    Ensure that all required imports are available.
    
    Returns:
        bool: True if imports are successful, False otherwise
    """
    return IMPORTS_AVAILABLE


def get_project_root() -> Path:
    """Get the project root directory."""
    return _PROJECT_ROOT


def get_src_path() -> Path:
    """Get the src directory path."""
    return _SRC_PATH


def create_test_symbol(
    name: str = "test_symbol",
    kind: Optional['SymbolKind'] = None,
    file_path: str = "test.sv",
    line: int = 1,
    column: int = 1
) -> Optional['SymbolInfo']:
    """
    Create a test symbol for testing purposes.
    
    Args:
        name: Symbol name
        kind: Symbol kind (defaults to VARIABLE)
        file_path: File path
        line: Line number
        column: Column number
        
    Returns:
        SymbolInfo object or None if imports not available
    """
    if not IMPORTS_AVAILABLE:
        return None
        
    if kind is None:
        kind = SymbolKind.VARIABLE
        
    location = Location(file_path=file_path, line=line, column=column)
    
    return SymbolInfo(
        name=name,
        kind=kind,
        definition_location=location,
        qualified_name=f"test_module.{name}",
        type_info="logic",
        documentation=f"Test symbol: {name}"
    )


def create_test_analyzer() -> Optional['SymbolAnalyzer']:
    """
    Create a SymbolAnalyzer instance for testing.
    
    Returns:
        SymbolAnalyzer instance or None if imports not available
    """
    if not IMPORTS_AVAILABLE:
        return None
        
    return SymbolAnalyzer()


def create_test_semantic_model() -> Optional['SemanticModel']:
    """
    Create a SemanticModel instance for testing.
    
    Returns:
        SemanticModel instance or None if imports not available
    """
    if not IMPORTS_AVAILABLE:
        return None
        
    return SemanticModel()


def get_sample_systemverilog_code() -> str:
    """
    Get sample SystemVerilog code for testing.
    
    Returns:
        Sample SystemVerilog code as string
    """
    return """
module test_module #(
    parameter int WIDTH = 8
) (
    input logic clk,
    input logic reset,
    output logic [WIDTH-1:0] data_out
);
    
    logic [WIDTH-1:0] counter;
    logic enable;
    
    function automatic logic [WIDTH-1:0] increment(
        input logic [WIDTH-1:0] value
    );
        return value + 1;
    endfunction
    
    always_ff @(posedge clk) begin
        if (reset) begin
            counter <= 0;
            enable <= 1'b0;
        end else begin
            if (enable) begin
                counter <= increment(counter);
            end
        end
    end
    
    assign data_out = counter;
    
endmodule

interface test_if(input logic clk);
    logic [31:0] data;
    logic valid;
    
    modport master (output data, valid);
    modport slave (input data, valid);
endinterface

class test_transaction;
    rand bit [31:0] addr;
    rand bit [31:0] data;
    
    function new();
        addr = 0;
        data = 0;
    endfunction
    
    virtual task execute();
        $display("Executing transaction");
    endtask
endclass
"""


def get_complex_systemverilog_code() -> str:
    """
    Get complex SystemVerilog code for advanced testing.
    
    Returns:
        Complex SystemVerilog code as string
    """
    return """
package test_pkg;
    typedef enum logic [1:0] {
        IDLE = 2'b00,
        ACTIVE = 2'b01,
        DONE = 2'b10
    } state_t;
    
    typedef struct packed {
        logic [7:0] data;
        logic valid;
        logic ready;
    } bus_t;
    
    parameter int DATA_WIDTH = 32;
    localparam int ADDR_WIDTH = 16;
endpackage

interface axi_if #(
    parameter int DATA_WIDTH = 32,
    parameter int ADDR_WIDTH = 32
) (
    input logic clk,
    input logic rst_n
);
    logic [ADDR_WIDTH-1:0] awaddr;
    logic awvalid;
    logic awready;
    
    modport master (
        output awaddr, awvalid,
        input awready
    );
    
    modport slave (
        input awaddr, awvalid,
        output awready
    );
endinterface

class transaction_base;
    rand bit [31:0] addr;
    rand bit [31:0] data;
    
    constraint addr_c {
        addr inside {[32'h1000:32'h2000]};
    }
    
    virtual function void print();
        $display("Transaction: addr=0x%h, data=0x%h", addr, data);
    endfunction
    
    virtual task execute();
        // Implementation
    endtask
endclass

module cpu_complex #(
    parameter int DATA_WIDTH = 32,
    parameter int CACHE_SIZE = 1024
) (
    input logic clk,
    input logic rst_n,
    axi_if.master mem_if,
    output logic [DATA_WIDTH-1:0] result
);
    
    import test_pkg::*;
    
    // Internal types
    typedef enum logic [2:0] {
        FETCH, DECODE, EXECUTE, WRITEBACK
    } pipeline_stage_t;
    
    // Internal signals
    logic [DATA_WIDTH-1:0] pc;
    logic [DATA_WIDTH-1:0] instruction;
    pipeline_stage_t current_stage;
    
    // Functions
    function automatic logic [DATA_WIDTH-1:0] alu_add(
        input logic [DATA_WIDTH-1:0] a,
        input logic [DATA_WIDTH-1:0] b
    );
        return a + b;
    endfunction
    
    // Tasks
    task automatic fetch_instruction();
        instruction <= pc;
    endtask
    
    // Always blocks
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pc <= 0;
            current_stage <= FETCH;
        end else begin
            case (current_stage)
                FETCH: begin
                    fetch_instruction();
                    current_stage <= DECODE;
                end
                DECODE: begin
                    current_stage <= EXECUTE;
                end
                EXECUTE: begin
                    result <= alu_add(pc, instruction);
                    current_stage <= WRITEBACK;
                end
                WRITEBACK: begin
                    current_stage <= FETCH;
                    pc <= pc + 4;
                end
            endcase
        end
    end
    
endmodule
"""


def run_test_with_timeout(test_func, timeout: float = 30.0) -> Any:
    """
    Run a test function with a timeout.
    
    Args:
        test_func: Test function to run
        timeout: Timeout in seconds
        
    Returns:
        Result of test function
    """
    import signal
    
    def timeout_handler(signum, frame):
        raise TimeoutError(f"Test timed out after {timeout} seconds")
    
    # Set up timeout
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(int(timeout))
    
    try:
        result = test_func()
        signal.alarm(0)  # Cancel timeout
        return result
    except TimeoutError:
        raise
    finally:
        signal.alarm(0)  # Ensure timeout is cancelled


def skip_if_no_imports(test_func):
    """
    Decorator to skip tests if imports are not available.
    
    Args:
        test_func: Test function to wrap
        
    Returns:
        Wrapped test function
    """
    import pytest
    
    def wrapper(*args, **kwargs):
        if not IMPORTS_AVAILABLE:
            pytest.skip("sv_lsp modules not available")
        return test_func(*args, **kwargs)
    
    return wrapper


def requires_pyslang(test_func):
    """
    Decorator to skip tests if pyslang is not available.
    
    Args:
        test_func: Test function to wrap
        
    Returns:
        Wrapped test function
    """
    import pytest
    
    def wrapper(*args, **kwargs):
        try:
            import pyslang
        except ImportError:
            pytest.skip("pyslang not available")
        return test_func(*args, **kwargs)
    
    return wrapper


# Export commonly used items
__all__ = [
    'ensure_imports',
    'get_project_root', 
    'get_src_path',
    'create_test_symbol',
    'create_test_analyzer',
    'create_test_semantic_model',
    'get_sample_systemverilog_code',
    'get_complex_systemverilog_code',
    'run_test_with_timeout',
    'skip_if_no_imports',
    'requires_pyslang',
    'IMPORTS_AVAILABLE'
]
