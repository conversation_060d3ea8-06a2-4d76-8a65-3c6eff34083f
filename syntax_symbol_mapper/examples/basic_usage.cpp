#include <iostream>
#include <string>
#include <iomanip>

#include "SyntaxToSymbolMapper.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"

using namespace syntax_symbol_mapper;
using namespace slang;

void printMappingStats(const SyntaxToSymbolMapper& mapper) {
    std::cout << "\n=== Mapping Statistics ===" << std::endl;
    std::cout << "Total mappings: " << mapper.getMappingCount() << std::endl;
    
    const auto& config = mapper.getConfig();
    std::cout << "Configuration:" << std::endl;
    std::cout << "  Caching enabled: " << (config.enable_caching ? "Yes" : "No") << std::endl;
    std::cout << "  Deep traversal: " << (config.deep_traversal ? "Yes" : "No") << std::endl;
    std::cout << "  Max cache size: " << config.max_cache_size << std::endl;
}

void demonstrateBasicUsage() {
    std::cout << "\n=== Basic Usage Demonstration ===" << std::endl;
    
    // SystemVerilog source code
    std::string source = R"(
module example_module;
    // Variable declarations
    int counter;
    logic [15:0] data_bus;
    logic enable, reset;
    
    // Parameter declaration
    parameter int WIDTH = 16;
    
    // Always block
    always @(posedge clk or posedge reset) begin
        if (reset) begin
            counter <= 0;
            data_bus <= 0;
        end else if (enable) begin
            counter <= counter + 1;
            data_bus <= counter[15:0];
        end
    end
    
    // Continuous assignment
    assign output_valid = (counter > 0);
    
endmodule
)";

    std::cout << "Source code:" << std::endl;
    std::cout << source << std::endl;
    
    // Parse the source
    auto tree = syntax::SyntaxTree::fromText(source);
    if (!tree) {
        std::cerr << "Failed to parse source code" << std::endl;
        return;
    }
    
    // Create compilation
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    
    // Get diagnostics to force elaboration
    auto diagnostics = compilation.getAllDiagnostics();
    std::cout << "Compilation diagnostics: " << diagnostics.size() << " issues" << std::endl;
    
    // Create mapper with custom configuration
    MapperConfig config;
    config.enable_caching = true;
    config.deep_traversal = true;
    config.max_cache_size = 5000;
    
    SyntaxToSymbolMapper mapper(compilation, config);
    
    // Build the mapping
    std::cout << "\nBuilding syntax-symbol mapping..." << std::endl;
    auto result = mapper.buildMapping(*tree);
    
    if (result.success) {
        std::cout << "✓ Mapping successful!" << std::endl;
        std::cout << "  Mapped " << result.mapped_count << " syntax-symbol pairs" << std::endl;
    } else {
        std::cout << "✗ Mapping failed: " << result.error_message << std::endl;
        return;
    }
    
    // Print statistics
    printMappingStats(mapper);
    
    // Demonstrate symbol lookup
    std::cout << "\n=== Symbol Lookup Examples ===" << std::endl;
    
    // Get the syntax-to-symbol mapping
    const auto& syntaxToSymbol = mapper.getSyntaxToSymbolMap();
    const auto& symbolToSyntax = mapper.getSymbolToSyntaxMap();
    
    std::cout << "Forward mapping (syntax -> symbol): " << syntaxToSymbol.size() << " entries" << std::endl;
    std::cout << "Reverse mapping (symbol -> syntax): " << symbolToSyntax.size() << " entries" << std::endl;
    
    // Show some example mappings
    int count = 0;
    for (const auto& [syntax, symbol] : syntaxToSymbol) {
        if (count >= 5) break; // Show first 5 mappings
        
        auto info = mapper.getSymbolInfo(syntax);
        std::cout << "  " << std::setw(20) << getSyntaxKindString(syntax) 
                  << " -> " << std::setw(20) << info.kind;
        if (!info.name.empty()) {
            std::cout << " (" << info.name << ")";
        }
        std::cout << " at " << info.location << std::endl;
        count++;
    }
    
    if (syntaxToSymbol.size() > 5) {
        std::cout << "  ... and " << (syntaxToSymbol.size() - 5) << " more mappings" << std::endl;
    }
}

void demonstrateAdvancedFeatures() {
    std::cout << "\n=== Advanced Features ===" << std::endl;
    
    // Create a more complex example
    std::string source = R"(
package test_pkg;
    typedef struct {
        int x, y;
    } point_t;
endpackage

module advanced_example;
    import test_pkg::*;
    
    point_t my_point;
    
    function automatic int calculate_distance(point_t p1, point_t p2);
        return (p1.x - p2.x) + (p1.y - p2.y);
    endfunction
    
    initial begin
        my_point.x = 10;
        my_point.y = 20;
    end
endmodule
)";

    auto tree = syntax::SyntaxTree::fromText(source);
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    
    auto diagnostics = compilation.getAllDiagnostics();
    
    SyntaxToSymbolMapper mapper(compilation);
    auto result = mapper.buildMapping(*tree);
    
    if (result.success) {
        std::cout << "✓ Advanced mapping successful!" << std::endl;
        printMappingStats(mapper);
        
        // Test configuration updates
        MapperConfig newConfig;
        newConfig.enable_caching = false;
        newConfig.deep_traversal = false;
        
        mapper.updateConfig(newConfig);
        std::cout << "\nUpdated configuration:" << std::endl;
        std::cout << "  Caching: " << (mapper.getConfig().enable_caching ? "enabled" : "disabled") << std::endl;
        std::cout << "  Deep traversal: " << (mapper.getConfig().deep_traversal ? "enabled" : "disabled") << std::endl;
    }
}

int main() {
    std::cout << "SyntaxToSymbolMapper Usage Examples" << std::endl;
    std::cout << "===================================" << std::endl;
    
    try {
        demonstrateBasicUsage();
        demonstrateAdvancedFeatures();
        
        std::cout << "\n✓ All examples completed successfully!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "\n✗ Example failed: " << e.what() << std::endl;
        return 1;
    }
}
