
import pyslang

def find_symbol_for_declaration_node(scope, target_node):
    """
    Recursively searches a semantic scope to find the Symbol that was
    created from a specific declaration SyntaxNode.
    """
    if not scope:
        return None

    for member in scope:
        if hasattr(member, 'syntax') and member.syntax == target_node:
            return member
        if isinstance(member, pyslang.Scope):
            found = find_symbol_for_declaration_node(member, target_node)
            if found:
                return found
    return None

# --- Main ---

source_code = """
module my_mod;
    int i; // The DECLARATION
    always @*
        i <= 1; // The USAGE
endmodule
"""

# 1. Compile for semantics
tree = pyslang.SyntaxTree.fromText(source_code)
compilation = pyslang.Compilation()
compilation.addSyntaxTree(tree)
list(compilation.getAllDiagnostics())

# 2. Identify the two nodes of interest
# Correctly navigate to the declaration and usage nodes
module_decl_node = tree.root.members[0]
data_decl_node = module_decl_node.members[0]
declaration_node = data_decl_node.declarators[0]

procedural_block_node = module_decl_node.members[1]
assignment_expr_node = procedural_block_node.statement.expr
usage_node = assignment_expr_node.left

print(f"Declaration Node: {declaration_node.kind} (Name: {declaration_node.name.valueText})")
print(f"Usage Node:       {usage_node.kind} (Name: {usage_node.identifier.valueText})")
print("-" * 40)

# 3. Resolve the DECLARATION node to its Symbol
print("Resolving declaration node...")
declaration_symbol = find_symbol_for_declaration_node(compilation.getRoot(), declaration_node)
if declaration_symbol:
    print(f"  -> Found Symbol: '{declaration_symbol.name}' (Kind: {declaration_symbol.kind})")
    print(f"  -> Declaration Location: {declaration_symbol.location}")
else:
    print("  -> Could not resolve declaration symbol.")
print("-" * 40)

# 4. Resolve the USAGE node to its Symbol
print("Resolving usage node...")
module_symbol = compilation.getRoot().lookupName("my_mod")
context = pyslang.ASTContext(module_symbol, pyslang.LookupLocation.max)
bound_expression = pyslang.Expression.bind(usage_node, context)
usage_symbol = bound_expression.getSymbolReference()

if usage_symbol:
    print(f"  -> Found Symbol: '{usage_symbol.name}' (Kind: {usage_symbol.kind})")
    print(f"  -> Declaration Location: {usage_symbol.location}")
else:
    print("  -> Could not resolve usage symbol.")
print("-" * 40)

# 5. Verify that both nodes resolved to the exact same object
print("Verification:")
if declaration_symbol and usage_symbol:
    are_same_object = declaration_symbol is usage_symbol
    print(f"Are they the same symbol object? -> {are_same_object}")
    assert are_same_object, "Verification failed: The symbols should be the same object!"
    print("Success! Both the 'Declarator' and 'IdentifierName' nodes correctly map to the same canonical Symbol.")
else:
    print("Could not perform verification because one or both symbols were not found.")

