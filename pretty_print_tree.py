

import pyslang

# A SystemVerilog code snippet with inconsistent indentation
source_code = """
module my_mod;
    int i;
    always @* begin
if (i > 0)
    i <= i - 1;
        else
            i <= 10;
    end
endmodule
"""

# 1. Parse the code into a SyntaxTree
tree = pyslang.SyntaxTree.fromText(source_code)

# 2. Create and configure the SyntaxPrinter
#    - setIncludeTrivia(False): Ignore original whitespace.
#    - setIncludeMissing(True):  Force printer to insert what it thinks is needed.
printer = pyslang.SyntaxPrinter()
printer.setIncludeTrivia(False)
printer.setIncludeMissing(True)

# 3. Print the tree and get the formatted string
printer.print(tree)
formatted_code = printer.str()

# --- Output ---
print("Original Code:")
print(source_code)
print("\nFormatted Code:")
print(formatted_code)

