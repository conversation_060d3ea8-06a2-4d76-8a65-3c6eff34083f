cmake_minimum_required(VERSION 3.20)

project(SyntaxSymbolMapper
    VERSION 1.0.0
    LANGUAGES CXX
    DESCRIPTION "C++ module for mapping SyntaxTree to Symbol using slang library"
)

# Set C++20 standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Find required packages (optional)
find_package(PkgConfig QUIET)

# Set slang library path
set(SLANG_ROOT_DIR "${CMAKE_CURRENT_SOURCE_DIR}/..")
set(SLANG_LIB_DIR "${SLANG_ROOT_DIR}/build/lib")
set(SLANG_INCLUDE_DIR "${SLANG_ROOT_DIR}/include")

# Check if slang library exists
if(NOT EXISTS "${SLANG_LIB_DIR}/libsvlang.a")
    message(FATAL_ERROR "slang library not found at ${SLANG_LIB_DIR}/libsvlang.a")
endif()

# Include directories
include_directories(
    ${SLANG_INCLUDE_DIR}
    ${SLANG_ROOT_DIR}/build/source
    ${SLANG_ROOT_DIR}/external
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Add compile options
add_compile_options(-Wall -Wextra -Wpedantic)
add_compile_definitions(SLANG_BOOST_SINGLE_HEADER)

# Debug/Release configurations
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_options(-g -O0 -DDEBUG)
else()
    add_compile_options(-O3 -DNDEBUG)
endif()

# Source files
set(SOURCES
    src/Common.cpp
    src/SyntaxToSymbolMapper.cpp
    src/SymbolResolver.cpp
    src/MappingCache.cpp
)

# Header files
set(HEADERS
    include/SyntaxToSymbolMapper.h
    include/SymbolResolver.h
    include/MappingCache.h
    include/Common.h
)

# Create the main library
add_library(syntax_symbol_mapper STATIC ${SOURCES} ${HEADERS})

# Link with slang library
target_link_libraries(syntax_symbol_mapper
    PRIVATE
    ${SLANG_LIB_DIR}/libsvlang.a
    ${SLANG_LIB_DIR}/libfmt.a
    ${SLANG_LIB_DIR}/libmimalloc.a
)

# Set target properties
set_target_properties(syntax_symbol_mapper PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    POSITION_INDEPENDENT_CODE ON
)

# Create test executable
add_executable(test_mapper
    tests/test_main.cpp
    tests/test_basic_mapping.cpp
    tests/test_symbol_resolution.cpp
)

# Create comprehensive test executable
add_executable(test_comprehensive
    tests/test_comprehensive.cpp
)

target_link_libraries(test_mapper
    PRIVATE
    syntax_symbol_mapper
    ${SLANG_LIB_DIR}/libsvlang.a
    ${SLANG_LIB_DIR}/libfmt.a
    ${SLANG_LIB_DIR}/libmimalloc.a
)

target_link_libraries(test_comprehensive
    PRIVATE
    syntax_symbol_mapper
    ${SLANG_LIB_DIR}/libsvlang.a
    ${SLANG_LIB_DIR}/libfmt.a
    ${SLANG_LIB_DIR}/libmimalloc.a
)

# Create example executable
add_executable(example_usage
    examples/basic_usage.cpp
)

target_link_libraries(example_usage
    PRIVATE
    syntax_symbol_mapper
    ${SLANG_LIB_DIR}/libsvlang.a
    ${SLANG_LIB_DIR}/libfmt.a
    ${SLANG_LIB_DIR}/libmimalloc.a
)

# Enable testing
enable_testing()
add_test(NAME mapper_tests COMMAND test_mapper)
add_test(NAME comprehensive_tests COMMAND test_comprehensive)

# Install targets
install(TARGETS syntax_symbol_mapper
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

install(DIRECTORY include/
    DESTINATION include/syntax_symbol_mapper
    FILES_MATCHING PATTERN "*.h"
)

# Print configuration summary
message(STATUS "=== SyntaxSymbolMapper Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Slang library: ${SLANG_LIB_DIR}/libsvlang.a")
message(STATUS "Slang includes: ${SLANG_INCLUDE_DIR}")
message(STATUS "========================================")
