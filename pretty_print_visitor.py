import pyslang

class PrettyPrinter:
    """
    A comprehensive visitor that traverses a SyntaxTree and builds a
    pretty-printed string with correct indentation.
    """
    def __init__(self, indent_char=' ', indent_width=4):
        self.result = []
        self.indent_level = 0
        self.indent_str = indent_char * indent_width
        self.needs_indent = True

    def _indent(self):
        """Applies the current indentation if needed."""
        if self.needs_indent:
            self.result.append(self.indent_str * self.indent_level)
            self.needs_indent = False

    def _newline(self):
        """Adds a newline and signals that the next write needs indentation."""
        if not self.result or not self.result[-1].endswith('\n'):
            self.result.append('\n')
        self.needs_indent = True

    def print(self, tree):
        """Prints the entire tree."""
        self.visit(tree.root)
        return "".join(self.result)

    def visit(self, node):
        """
        Generic visitor entry point that dispatches to a specific
        handler based on the node's kind.
        """
        if node is None:
            return

        method_name = f'visit_{node.kind.name}'
        handler = getattr(self, method_name, self.visit_generic)
        handler(node)

    def visit_generic(self, node):
        """
        A generic handler for nodes that don't have a specific one.
        It prints the node's constituent tokens.
        """
        self._indent()
        for child in node:
            if isinstance(child, pyslang.Token):
                self.result.append(child.valueText)
                # Add spacing after keywords and identifiers for readability
                if child.kind.name.endswith("Keyword") or child.kind.name in ['Identifier', 'IntegerLiteral']:
                    self.result.append(" ")
            else:
                self.visit(child)

    # --- Scope and Block Handlers ---

    def visit_ModuleDeclaration(self, node):
        self._indent()
        self.result.append(f"module {node.header.name.valueText}")
        if node.header.ports:
            self.visit(node.header.ports)
        self.result.append(";")
        self._newline()

        self.indent_level += 1
        for member in node.members:
            self.visit(member)
        self.indent_level -= 1

        self._indent()
        self.result.append("endmodule")
        self._newline()

    def visit_SequentialBlockStatement(self, node):
        self.result.append("begin")
        self._newline()
        self.indent_level += 1
        for item in node.items:
            self.visit(item)
        self.indent_level -= 1
        self._indent()
        self.result.append("end")

    def visit_ProceduralBlock(self, node):
        self._indent()
        self.result.append(f"{node.keyword.valueText} @* ")
        self.visit(node.statement)
        self._newline()

    # --- Declaration and Statement Handlers ---

    def visit_DataDeclaration(self, node):
        self._indent()
        self.visit(node.type)
        self.result.append(" ")

        for i, decl in enumerate(node.declarators):
            self.visit(decl)
            if i < len(node.declarators) - 1:
                self.result.append(", ")

        self.result.append(";")
        self._newline()

    def visit_Declarator(self, node):
        self.result.append(node.name.valueText)

    def visit_ExpressionStatement(self, node):
        self._indent()
        self.visit(node.expr)
        self.result.append(";")
        self._newline()

    def visit_ConditionalStatement(self, node):
        self._indent()
        self.result.append("if (")
        self.visit(node.predicate.conditions[0].expr)
        self.result.append(") ")

        is_block = node.statement.kind == pyslang.SyntaxKind.SequentialBlockStatement
        if not is_block:
            self._newline()
            self.indent_level += 1

        self.visit(node.statement)

        if not is_block:
            self.indent_level -= 1

        if node.elseClause:
            self._indent()
            self.result.append("else ")

            else_is_block = node.elseClause.clause.kind == pyslang.SyntaxKind.SequentialBlockStatement
            if not else_is_block:
                self._newline()
                self.indent_level += 1

            self.visit(node.elseClause.clause)

            if not else_is_block:
                self.indent_level -= 1

    # --- Expression Handlers ---

    def visit_AssignmentExpression(self, node):
        self.visit(node.left)
        self.result.append(f" {node.operator.valueText} ")
        self.visit(node.right)

    def visit_BinaryExpression(self, node):
        self.visit(node.left)
        self.result.append(f" {node.operator.valueText} ")
        self.visit(node.right)

    def visit_IdentifierName(self, node):
        self.result.append(node.identifier.valueText)

    def visit_IntegerLiteralExpression(self, node):
        self.result.append(node.literal.valueText)

    def visit_IntegerType(self, node):
        self.result.append(node.keyword.valueText)

# --- Main ---

source_code = """
module my_mod;
    int i;
    always @* begin
if (i > 0)
    i <= i - 1;
        else
            i <= 10;
    end
endmodule
"""

tree = pyslang.SyntaxTree.fromText(source_code)

pretty_printer = PrettyPrinter()
formatted_code = pretty_printer.print(tree)

print("Original Code:")
print(source_code)
print("\n--- Pretty-Printed Code ---")
print(formatted_code)