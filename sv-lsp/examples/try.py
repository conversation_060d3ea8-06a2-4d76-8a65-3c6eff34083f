import pyslang

f = open('visit.log', 'w')


class Visitor:
    def __init__(self) -> None:
        self.indent = 1
        self.syntax2symbol: dict[pyslang.SyntaxNode, pyslang.Symbol] = {}


    def init(self, tree: pyslang.SyntaxTree, cmpl: pyslang.Compilation):
        self.tree = tree
        self.cmpl = cmpl
        self.syntax2symbol.clear()
        return self


    def get_declared_symbol(self, syntax: pyslang.SyntaxNode):
        if syntax in self.syntax2symbol:
            return self.syntax2symbol[syntax]

        print(f'get_declared_symbol: {syntax.kind.name}')

        # Try to resolve the symbol
        if syntax.kind.name == 'CompilationUnit':
            symbol = self.cmpl.getCompilationUnit(syntax)
            if symbol:
                self.syntax2symbol[syntax] = symbol
            return symbol
        elif syntax.kind.name in ['ModuleDeclaration', 'InterfaceDeclaration', 'ProgramDeclaration']:
            assert isinstance(syntax, pyslang.ModuleDeclarationSyntax)
            parent = self.get_parent(syntax)
            if not parent:
                return None
            if not parent.isScope:
                parent = self.cmpl.getRoot()
            defi = self.cmpl.tryGetDefinition(syntax.header.name.value, parent)
            if not defi:
                return None
            symbol = pyslang.InstanceSymbol()  # There is no symbol to use here so create a placeholder instance.
            self.syntax2symbol[syntax] = symbol
            return symbol

        parent = self.get_parent(syntax)
        if not parent:
            return None

        if parent.kind.name == 'TypeAlias':
            assert isinstance(parent, pyslang.TypeAliasType)
            target = parent.targetType.type
            if target.syntax == syntax:
                self.syntax2symbol[syntax] = target
                return target
            return None

        if not parent.isScope:
            return None

        for member in parent.members():
            if member.syntax == syntax:
                self.syntax2symbol[syntax] = member
                return member

        return None


    def visit_syntax(self, syntax: pyslang.SyntaxNode):
        symbol = self.get_declared_symbol(syntax)
        if symbol:
            name = str(symbol.name).strip() if hasattr(symbol, 'name') else '<noname>'
            kind = symbol.kind.name if hasattr(symbol, 'kind') else '<nokind>'
            hier = symbol.hierarchicalPath if hasattr(symbol, 'hierarchicalPath') else '<nohier>'
            if hier != '<nohier>':
                self.indent = len(hier.split('.') * 2)
            print(f'{"":<{self.indent}}{name:{20-self.indent}} {kind:40} {hier}', file=f)
        else:
            print(f'{"":<{self.indent}}<nosymbol> {syntax.kind.name:40}')


    def get_parent(self, syntax: pyslang.SyntaxNode) -> pyslang.Symbol | None:
        parent = self.get_declared_symbol(syntax.parent) if syntax.parent else None
        if not parent:
            return None

        if parent.kind.name == 'Instance':
            assert isinstance(parent, pyslang.InstanceSymbol)
            parent = parent.body

        return parent


    definition = None
    body = None
    def visit(self, symbol):
        if isinstance(symbol, pyslang.Token):
            return
        name = str(symbol.name).strip() if hasattr(symbol, 'name') else '<noname>'
        kind = symbol.kind.name if hasattr(symbol, 'kind') else '<nokind>'
        hier = symbol.hierarchicalPath if hasattr(symbol, 'hierarchicalPath') else '<nohier>'
        if hier != '<nohier>':
            self.indent = len(hier.split('.') * 2)
        print(f'{"":<{self.indent}}{name:{20-self.indent}} {kind:40} {hier}', file=f)
        if hier == 'counter.u_if1' or hier == 'counter.u_if2':
            print(symbol.definition, self.definition == symbol.definition if self.definition else '<nocheck>')
            self.definition = symbol.definition
            if isinstance(symbol, pyslang.InstanceBodySymbol):
                print(symbol, self.body == symbol)
                self.body = symbol


class SyntaxTreePrinter:
    """
    A visitor that traverses a SyntaxTree and prints the kind and name
    of each node with indentation to show the tree structure.
    """
    def __init__(self, indent_char='.', indent_width=2):
        self.result = []
        self.indent_level = 0
        self.indent_str = indent_char * indent_width
        self.syntax2symbol: dict[str, pyslang.Symbol] = {}

    def print(self, tree):
        """Prints the entire tree structure."""
        self.visit(tree.root)
        return "".join(self.result)

    def visit(self, node):
        """
        Generic visitor that prints the current node's info and then
        recursively visits its children.
        """
        if node is None:
            return
        # 1. Indent and print the current node's information
        self.result.append(self.indent_str * self.indent_level)

        kind_name = node.kind.name
        node_name = ""

        parent = ''
        if isinstance(node, pyslang.Token):
            # For tokens, show the value text
            node_name = f" (Text: '{node.valueText}')"
        elif hasattr(node, 'name') and hasattr(node.name, 'valueText'):
            node_name = f" (Name: {node.name.valueText})"
        elif isinstance(node, pyslang.IdentifierNameSyntax):
            node_name = f" (Iden: {node.identifier.valueText})"
        parent_name = ''
        if isinstance(node, pyslang.SyntaxNode):
            # parent = self.get_parent(node)
            # if parent:
            #     parent_name = f' (Parent: {str(parent.name).strip()})'

            # test
            if hasattr(node, 'name'):
                print(str(node.name).strip())
                symbol = self.get_declared_symbol(str(node.name).strip(), node)
                print(type(symbol))
                if symbol is not None:
                    print('>'*50, symbol.hierarchicalPath)

        self.result.append(f"- {kind_name}{node_name} {parent_name}\n")

        # 2. Recurse into children only if it's a SyntaxNode, not a Token
        if isinstance(node, pyslang.SyntaxNode):
            self.indent_level += 1
            for child in node:
                self.visit(child)
            self.indent_level -= 1


    def init(self, tree: pyslang.SyntaxTree, cmpl: pyslang.Compilation):
        self.tree = tree
        self.cmpl = cmpl
        self.syntax2symbol.clear()
        self.cmpl_visited = False
        # self.visit_cmpl()
        return self


    def get_declared_symbol(self, name: str, syntax: pyslang.SyntaxNode, indent=1) -> pyslang.Symbol | None:
        # Try to resolve the symbol
        indent_str = ' '*(2*indent-1)
        current = syntax
        while current.kind.name != 'CompilationUnit':
            parent = self.get_parent(current)
            if not parent:
                return None

            parent_name = str(parent.name).strip() if parent.kind.name != 'CompilationUnit' else 'CompilationUnit'
            if parent.kind.name == 'ModuleHeader':
                parent = parent.parent
                assert parent.kind.name == 'ModuleDeclaration'

            print(indent_str, parent_name, type(parent))

            parent_symbol = self.get_declared_symbol(parent_name, parent, indent+1)
            if not parent_symbol:
                current = self.tree.root
                parent_symbol = self.cmpl.getRoot()
            print(indent_str, f'{parent_symbol = }, {parent_symbol.lookupName(name) = }')

            if not parent_symbol.isScope:
                current = parent
                print(indent_str, 'not a scope')
                continue

            symbol = parent_symbol.lookupName(name)
            print(indent_str, f'{symbol = }, {bool(symbol) = }')
            if symbol is not None   :
                assert isinstance(symbol, pyslang.Symbol), f'{type(symbol)}'
                print(indent_str, f'lookup {name} ok')
                if isinstance(symbol, pyslang.InstanceSymbol):
                    symbol = symbol.body
                return symbol
            else:
                print(indent_str, f'lookup {name} failed')
                # exit()

            current = parent


    def get_parent(self, syntax: pyslang.SyntaxNode) -> pyslang.SyntaxNode | None:
        if not syntax.parent:
            return None
        current = syntax
        if current.kind.name == 'CompilationUnit':
            return current
        while current.parent and not hasattr(current.parent, 'name'):
            current = current.parent
            print(current.kind.name)
            if current.kind.name == 'ModuleDeclaration':
                assert isinstance(current, pyslang.ModuleDeclarationSyntax)
                return current.header
            if current.kind.name == 'CompilationUnit':
                return current
        return current.parent


    def visit_cmpl(self):
        if self.cmpl_visited:
            return

        self.definition2body: dict[str, pyslang.InstanceBodySymbol] = {}

        def visit(symbol):
            if isinstance(symbol, pyslang.InstanceBodySymbol):
                defi = symbol.definition
                self.definition2body[defi.lexicalPath] = symbol

        for inst in self.cmpl.getRoot().topInstances:
            inst.visit(visit)

        self.cmpl_visited = True


tree = pyslang.SyntaxTree.fromFile('simple_module.sv')

opts = pyslang.CompilationOptions()
opts.flags = pyslang.CompilationFlags.None_
bag = pyslang.Bag()
bag.compilationOptions = opts
cmpl = pyslang.Compilation(options=bag)
print(cmpl.options.flags)
cmpl.addSyntaxTree(tree)

print(tree.root.kind.name)
unit = cmpl.getCompilationUnit(tree.root)
assert isinstance(unit, pyslang.CompilationUnitSymbol)
print(f'{unit = }, {type(unit) = }')
lookup = cmpl.tryGetDefinition('counter', unit)
assert isinstance(lookup, pyslang.Compilation.DefinitionLookupResult)
print(cmpl.sourceManager.getLineNumber(lookup.definition.location))
counter = lookup.definition
assert isinstance(counter, pyslang.DefinitionSymbol)
print(f'{type(counter) = }, {counter.name = }')
counter = tree.root.members[0]
print(f'{counter = }, {counter.kind.name = }, {counter.parent = }')
# tree.root.visit(Visitor().init(tree, cmpl).visit)
print('-'*80)
print(SyntaxTreePrinter().init(tree, cmpl).print(tree), file=f)
print('-'*80, file=f)

root = cmpl.getRoot()
top_insts = root.topInstances
for top in top_insts:
    top.visit(Visitor().init(tree, cmpl).visit)
