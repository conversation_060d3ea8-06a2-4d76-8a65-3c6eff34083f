import pyslang

class SyntaxTreePrinter:
    """
    A visitor that traverses a SyntaxTree and prints the kind and name
    of each node with indentation to show the tree structure.
    """
    def __init__(self, indent_char='.', indent_width=2):
        self.result = []
        self.indent_level = 0
        self.indent_str = indent_char * indent_width

    def print(self, tree):
        """Prints the entire tree structure."""
        self.visit(tree.root)
        return "".join(self.result)

    def visit(self, node):
        """
        Generic visitor that prints the current node's info and then
        recursively visits its children.
        """
        if node is None:
            return

        # 1. Indent and print the current node's information
        self.result.append(self.indent_str * self.indent_level)
        
        kind_name = node.kind.name
        node_name = ""
        
        if isinstance(node, pyslang.Token):
            # For tokens, show the value text
            node_name = f" (Text: '{node.valueText}')"
        elif hasattr(node, 'name') and hasattr(node.name, 'valueText'):
            node_name = f" (Name: {node.name.valueText})"
        elif isinstance(node, pyslang.IdentifierNameSyntax):
            node_name = f" (Name: {node.identifier.valueText})"

        self.result.append(f"- {kind_name}{node_name}\n")

        # 2. Recurse into children only if it's a SyntaxNode, not a Token
        if isinstance(node, pyslang.SyntaxNode):
            self.indent_level += 1
            for child in node:
                self.visit(child)
            self.indent_level -= 1


# --- Main ---

source_code = """
module my_mod;
    int i;
    always @*
        i <= 1;
endmodule
"""

tree = pyslang.SyntaxTree.fromText(source_code)

tree_printer = SyntaxTreePrinter()
tree_structure = tree_printer.print(tree)

print("--- Original Code ---")
print(source_code)
print("\n--- Indented Syntax Tree Structure ---")
print(tree_structure)