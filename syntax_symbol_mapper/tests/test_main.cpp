#include <iostream>
#include <cassert>
#include "SyntaxToSymbolMapper.h"

// Forward declarations for test functions
void test_basic_mapping();
void test_symbol_resolution();

int main() {
    std::cout << "Running SyntaxToSymbolMapper tests..." << std::endl;
    
    try {
        test_basic_mapping();
        std::cout << "✓ Basic mapping tests passed" << std::endl;
        
        test_symbol_resolution();
        std::cout << "✓ Symbol resolution tests passed" << std::endl;
        
        std::cout << "All tests passed!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Test failed: " << e.what() << std::endl;
        return 1;
    }
}
