#include "MappingCache.h"
#include <list>
#include <unordered_map>

namespace syntax_symbol_mapper {

class MappingCache::Impl {
public:
    explicit Impl(size_t max_size) : maxSize_(max_size), hits_(0), misses_(0), evictions_(0) {}
    
    void addMapping(const SyntaxNode* syntax, const Symbol* symbol) {
        if (!syntax || !symbol) return;
        
        // Check if already exists
        auto syntaxIt = syntaxToSymbol_.find(syntax);
        if (syntaxIt != syntaxToSymbol_.end()) {
            // Move to front (most recently used)
            accessOrder_.splice(accessOrder_.begin(), accessOrder_, syntaxIt->second.listIt);
            return;
        }
        
        // Add new mapping
        if (syntaxToSymbol_.size() >= maxSize_) {
            evictLRU();
        }
        
        // Add to front of access list
        accessOrder_.push_front({syntax, symbol});
        auto listIt = accessOrder_.begin();
        
        // Add to hash maps
        CacheEntry entry{symbol, listIt};
        syntaxToSymbol_[syntax] = entry;
        symbolToSyntax_[symbol] = syntax;
    }
    
    const Symbol* findSymbol(const SyntaxNode* syntax) const {
        auto it = syntaxToSymbol_.find(syntax);
        if (it != syntaxToSymbol_.end()) {
            // Move to front (most recently used) - const_cast for LRU update
            auto& mutableThis = const_cast<Impl&>(*this);
            mutableThis.accessOrder_.splice(mutableThis.accessOrder_.begin(), 
                                          mutableThis.accessOrder_, it->second.listIt);
            ++hits_;
            return it->second.symbol;
        }
        ++misses_;
        return nullptr;
    }
    
    const SyntaxNode* findSyntax(const Symbol* symbol) const {
        auto it = symbolToSyntax_.find(symbol);
        if (it != symbolToSyntax_.end()) {
            // Find in syntax map to update LRU
            auto syntaxIt = syntaxToSymbol_.find(it->second);
            if (syntaxIt != syntaxToSymbol_.end()) {
                auto& mutableThis = const_cast<Impl&>(*this);
                mutableThis.accessOrder_.splice(mutableThis.accessOrder_.begin(), 
                                              mutableThis.accessOrder_, syntaxIt->second.listIt);
            }
            ++hits_;
            return it->second;
        }
        ++misses_;
        return nullptr;
    }
    
    bool contains(const SyntaxNode* syntax) const {
        return syntaxToSymbol_.find(syntax) != syntaxToSymbol_.end();
    }
    
    bool contains(const Symbol* symbol) const {
        return symbolToSyntax_.find(symbol) != symbolToSyntax_.end();
    }
    
    void clear() {
        syntaxToSymbol_.clear();
        symbolToSyntax_.clear();
        accessOrder_.clear();
        hits_ = 0;
        misses_ = 0;
        evictions_ = 0;
    }
    
    size_t size() const {
        return syntaxToSymbol_.size();
    }
    
    size_t maxSize() const {
        return maxSize_;
    }
    
    void setMaxSize(size_t max_size) {
        maxSize_ = max_size;
        while (syntaxToSymbol_.size() > maxSize_) {
            evictLRU();
        }
    }
    
    double getHitRate() const {
        size_t total = hits_ + misses_;
        return total > 0 ? static_cast<double>(hits_) / total : 0.0;
    }
    
    MappingCache::CacheStats getStats() const {
        return {
            hits_,
            misses_,
            evictions_,
            syntaxToSymbol_.size(),
            maxSize_,
            getHitRate()
        };
    }
    
    void resetStats() {
        hits_ = 0;
        misses_ = 0;
        evictions_ = 0;
    }

private:
    struct AccessItem {
        const SyntaxNode* syntax;
        const Symbol* symbol;
    };
    
    struct CacheEntry {
        const Symbol* symbol;
        std::list<AccessItem>::iterator listIt;
    };
    
    void evictLRU() {
        if (accessOrder_.empty()) return;
        
        // Remove least recently used (back of list)
        auto& lru = accessOrder_.back();
        syntaxToSymbol_.erase(lru.syntax);
        symbolToSyntax_.erase(lru.symbol);
        accessOrder_.pop_back();
        ++evictions_;
    }
    
    size_t maxSize_;
    mutable size_t hits_;
    mutable size_t misses_;
    size_t evictions_;
    
    // LRU list: front = most recent, back = least recent
    std::list<AccessItem> accessOrder_;
    
    // Hash maps for O(1) lookup
    std::unordered_map<const SyntaxNode*, CacheEntry> syntaxToSymbol_;
    std::unordered_map<const Symbol*, const SyntaxNode*> symbolToSyntax_;
};

// Implementation of public interface
MappingCache::MappingCache(size_t max_size)
    : pImpl(std::make_unique<Impl>(max_size)) {
}

MappingCache::~MappingCache() = default;

void MappingCache::addMapping(const SyntaxNode* syntax, const Symbol* symbol) {
    pImpl->addMapping(syntax, symbol);
}

const Symbol* MappingCache::findSymbol(const SyntaxNode* syntax) const {
    return pImpl->findSymbol(syntax);
}

const SyntaxNode* MappingCache::findSyntax(const Symbol* symbol) const {
    return pImpl->findSyntax(symbol);
}

bool MappingCache::contains(const SyntaxNode* syntax) const {
    return pImpl->contains(syntax);
}

bool MappingCache::contains(const Symbol* symbol) const {
    return pImpl->contains(symbol);
}

void MappingCache::clear() {
    pImpl->clear();
}

size_t MappingCache::size() const {
    return pImpl->size();
}

size_t MappingCache::maxSize() const {
    return pImpl->maxSize();
}

void MappingCache::setMaxSize(size_t max_size) {
    pImpl->setMaxSize(max_size);
}

double MappingCache::getHitRate() const {
    return pImpl->getHitRate();
}

MappingCache::CacheStats MappingCache::getStats() const {
    return pImpl->getStats();
}

void MappingCache::resetStats() {
    pImpl->resetStats();
}

} // namespace syntax_symbol_mapper
