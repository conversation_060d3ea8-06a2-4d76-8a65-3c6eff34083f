#pragma once

#include "Common.h"

namespace syntax_symbol_mapper {

/**
 * @brief Helper class for resolving symbols from syntax nodes
 * 
 * This class implements the core algorithm for finding symbols that correspond
 * to syntax nodes, using scope traversal and name resolution.
 */
class SymbolResolver {
public:
    /**
     * @brief Constructor
     * @param compilation The slang compilation object
     */
    explicit SymbolResolver(Compilation& compilation);
    
    /**
     * @brief Destructor
     */
    ~SymbolResolver();
    
    /**
     * @brief Resolve symbol for a syntax node
     * @param syntax The syntax node to resolve
     * @return Pointer to the resolved symbol, or nullptr if not found
     */
    const Symbol* resolveSymbol(const SyntaxNode* syntax);
    
    /**
     * @brief Resolve symbol for an identifier node
     * @param identifierNode The identifier syntax node
     * @return Pointer to the resolved symbol, or nullptr if not found
     */
    const Symbol* resolveIdentifier(const SyntaxNode* identifierNode);
    
    /**
     * @brief Find the symbol that was created from a specific syntax node
     * @param syntax The syntax node that created the symbol
     * @return Pointer to the symbol, or nullptr if not found
     */
    const Symbol* findSymbolFromSyntax(const SyntaxNode* syntax);
    
    /**
     * @brief Get the scope symbol for a syntax node
     * @param syntax The syntax node representing a scope
     * @return Pointer to the scope symbol, or nullptr if not a scope
     */
    const Scope* getScopeForSyntax(const SyntaxNode* syntax);
    
    /**
     * @brief Traverse up the syntax tree to find the containing scope
     * @param syntax Starting syntax node
     * @return Pointer to the containing scope, or nullptr if not found
     */
    const Scope* findContainingScope(const SyntaxNode* syntax);
    
    /**
     * @brief Check if a syntax node represents a symbol declaration
     * @param syntax The syntax node to check
     * @return True if the node declares a symbol
     */
    bool isSymbolDeclaration(const SyntaxNode* syntax);
    
    /**
     * @brief Check if a syntax node represents an identifier reference
     * @param syntax The syntax node to check
     * @return True if the node is an identifier reference
     */
    bool isIdentifierReference(const SyntaxNode* syntax);
    
    /**
     * @brief Get the identifier name from a syntax node
     * @param syntax The syntax node
     * @return The identifier name, or empty string if not an identifier
     */
    std::string getIdentifierName(const SyntaxNode* syntax);
    
    /**
     * @brief Get qualified name for a syntax node
     * @param syntax The syntax node
     * @return Hierarchical name path
     */
    std::string getQualifiedName(const SyntaxNode* syntax);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace syntax_symbol_mapper
