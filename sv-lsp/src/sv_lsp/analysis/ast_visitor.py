import pyslang

from ..core.types import Symbol<PERSON>n<PERSON>, SymbolKind, Location
from ..core.semantic_model import SemanticModel
from ..core.symbol_kind_mapper import SymbolKindMapper
from ..utils.pyslang_wrapper import PYSLANG_AVAILABLE


class AstVisitor:
    def __init__(self):
        self.tree: pyslang.SyntaxTree


    def init(self, tree: pyslang.SyntaxTree):
        self.tree = tree


    def _is_ignored(self, node):
        if isinstance(node, pyslang.Token):
            return True
        if node.kind.name == 'SyntaxList':
            return True
        if node.kind.name == 'TokenList':
            return True
        if node.kind.name == 'SeparatedList':
            return True

        return False


    def visit(self, node):
        if self._is_ignored(node):
            return

        try:
            kind_str = node.kind.name
            location = self._get_location_from_pyslang(node)
            # print(f'{location = }')

            # Handle different node types comprehensively
            symbol = self._create_symbol_from_node(node, str(node.kind), location)
            # symbol_name = symbol.name if symbol else "None"
            # symbol_kind = symbol.kind.name if symbol else "None"
            # if hasattr(node, 'name'):
            #     if hasattr(node.name, 'value'):
            #         node_name = node.name.value
            #     else:
            #         node_name = str(node.name)
            # else:
            #     node_name = "None"
            # print(f'visit_node: {node_name.strip():20}, {kind_str:35}, {symbol_name:20}, {symbol_kind:10}, {location}')

            return symbol

        except Exception as e:
            exit(-1)
            logger.debug(f"Error processing node {kind_str}: {e}")



    def _get_location_from_pyslang(self, pyslang_node) -> Location:
        """Get location from pyslang node."""
        if hasattr(pyslang_node, 'sourceRange'):
            source_range = pyslang_node.sourceRange
            assert isinstance(source_range, pyslang.SourceRange)
            start = source_range.start
            return Location(
                file_path=self.tree.sourceManager.getFileName(start),
                line=self.tree.sourceManager.getLineNumber(start),
                column=self.tree.sourceManager.getColumnNumber(start)
            )
        else:
            print(f'No source range for node {pyslang_node}')
            return Location(file_path="<unknown>", line=0, column=0)


    def _create_symbol_from_node(self, node, kind: str, location: Location) -> Optional[SymbolInfo]:
        """
        Create a symbol from a pyslang node based on its kind.

        Args:
            node: pyslang syntax node
            kind_str: String representation of node kind
            location: Location of the node

        Returns:
            SymbolInfo if a symbol should be created, None otherwise
        """
        # Module and interface declarations
        if kind == "SyntaxKind.ModuleDeclaration":
            return self._create_module_symbol(node, location)
        elif kind == "SyntaxKind.InterfaceDeclaration":
            return self._create_interface_symbol(node, location)
        elif kind == "SyntaxKind.ProgramDeclaration":
            return self._create_program_symbol(node, location)
        elif kind == "SyntaxKind.PackageDeclaration":
            return self._create_package_symbol(node, location)

        # Class declarations
        elif kind == "SyntaxKind.ClassDeclaration":
            return self._create_class_symbol(node, location)

        # Function and task declarations
        elif kind == "SyntaxKind.FunctionDeclaration":
            return self._create_function_symbol(node, location)
        elif kind == "SyntaxKind.TaskDeclaration":
            return self._create_task_symbol(node, location)

        # Variable and net declarations
        elif kind == "SyntaxKind.DataDeclaration":
            return self._create_variable_symbol(node, location)
        elif kind == "SyntaxKind.NetDeclaration":
            return self._create_net_symbol(node, location)

        # Parameter declarations
        elif kind == "SyntaxKind.ParameterDeclaration":
            return self._create_parameter_symbol(node, location)
        elif kind == "SyntaxKind.LocalparamDeclaration":
            return self._create_localparam_symbol(node, location)

        # Port declarations
        elif kind == "SyntaxKind.ImplicitAnsiPort":
            return self._create_port_symbol(node, location)
        elif kind == "SyntaxKind.ExplicitAnsiPort":
            return self._create_explicit_port_symbol(node, location)

        # Type declarations
        elif kind == "SyntaxKind.TypedefDeclaration":
            return self._create_typedef_symbol(node, location)
        elif kind == "SyntaxKind.EnumType":
            return self._create_enum_symbol(node, location)
        elif kind == "SyntaxKind.StructType":
            return self._create_struct_symbol(node, location)
        elif kind == "SyntaxKind.UnionType":
            return self._create_union_symbol(node, location)

        # Generate blocks
        elif kind == "SyntaxKind.IfGenerate":
            return self._create_generate_block_symbol(node, location)
        elif kind == "SyntaxKind.LoopGenerate":
            return self._create_generate_loop_symbol(node, location)

        # Procedural blocks
        elif kind == "SyntaxKind.AlwaysBlock":
            return self._create_always_block_symbol(node, location)
        elif kind == "SyntaxKind.InitialBlock":
            return self._create_initial_block_symbol(node, location)
        elif kind == "SyntaxKind.FinalBlock":
            return self._create_final_block_symbol(node, location)

        # Assertion and property declarations
        elif kind == "SyntaxKind.PropertyDeclaration":
            return self._create_property_symbol(node, location)
        elif kind == "SyntaxKind.SequenceDeclaration":
            return self._create_sequence_symbol(node, location)

        # Covergroup declarations
        elif kind == "SyntaxKind.CovergroupDeclaration":
            return self._create_covergroup_symbol(node, location)

        # Clocking blocks
        elif kind == "SyntaxKind.ClockingDeclaration":
            return self._create_clocking_symbol(node, location)

        # Modport declarations
        elif kind == "SyntaxKind.ModportDeclaration":
            return self._create_modport_symbol(node, location)

        # Instance declarations
        elif kind == "SyntaxKind.HierarchyInstantiation":
            return self._create_instance_symbol(node, location)

        return None


    def _create_module_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a module symbol."""
        assert isinstance(node, pyslang.ModuleDeclarationSyntax)
        name = self._extract_identifier_from_node(node, "module")
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.MODULE,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _create_interface_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create an interface symbol."""
        name = self._extract_identifier_from_node(node, "interface")
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.INSTANCE,  # Interface instances
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _create_class_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        assert isinstance(node, pyslang.ClassDeclarationSyntax)
        name = node.name.value
        if name:
            result = SymbolInfo(
                name=name,
                kind=SymbolKind.CLASS_TYPE,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
            return result
        return None

    def _create_function_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a function symbol."""
        name = self._extract_identifier_from_node(node, "function")
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.SUBROUTINE,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _create_variable_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a variable symbol."""
        names = self._extract_variable_names_from_node(node)
        if names:
            # Return the first variable (could be extended to return all)
            return SymbolInfo(
                name=names[0],
                kind=SymbolKind.VARIABLE,
                definition_location=location,
                qualified_name=self._build_qualified_name(names[0])
            )
        return None

    def _create_port_symbol(self, node, location: Location) -> Optional[SymbolInfo]:
        """Create a port symbol."""
        name = self._extract_port_name_from_node(node)
        if name:
            return SymbolInfo(
                name=name,
                kind=SymbolKind.PORT,
                definition_location=location,
                qualified_name=self._build_qualified_name(name)
            )
        return None

    def _extract_identifier_from_node(self, node, keyword: str) -> Optional[str]:
        """Extract identifier following a keyword from a node."""
        try:
            text = str(node).strip()
            if keyword in text:
                parts = text.split()

                # Find the keyword position
                keyword_index = -1
                for i, part in enumerate(parts):
                    if part == keyword:
                        keyword_index = i
                        break

                if keyword_index == -1:
                    return None

                # For functions, skip qualifiers like 'automatic', 'static', return type
                if keyword == "function":
                    return self._extract_function_name(parts, keyword_index)
                elif keyword == "task":
                    return self._extract_task_name(parts, keyword_index)
                else:
                    # For other keywords, take the next identifier
                    for i in range(keyword_index + 1, len(parts)):
                        name = parts[i].split('(')[0].split(';')[0].strip()
                        if name.isidentifier():
                            return name
            return None
        except Exception as e:
            logger.debug(f"Error extracting identifier for {keyword}: {e}")
            return None

    def _extract_function_name(self, parts: List[str], keyword_index: int) -> Optional[str]:
        """Extract function name, skipping qualifiers and return type."""
        try:
            # Function syntax: [qualifiers] function [return_type] name(...)
            # Qualifiers: automatic, static
            # Return type: void, logic, int, etc.

            qualifiers = {'automatic', 'static'}
            type_keywords = {'void', 'logic', 'int', 'bit', 'byte', 'shortint', 'longint', 'real', 'string'}

            i = keyword_index + 1
            while i < len(parts):
                part = parts[i].split('(')[0].split(';')[0].strip()

                # Skip qualifiers
                if part in qualifiers:
                    i += 1
                    continue

                # Skip return type (but be careful with user-defined types)
                if part in type_keywords:
                    i += 1
                    continue

                # Skip array dimensions like [7:0]
                if part.startswith('[') or part.endswith(']'):
                    i += 1
                    continue

                # This should be the function name
                if part.isidentifier():
                    return part

                i += 1

            return None
        except Exception as e:
            logger.debug(f"Error extracting function name: {e}")
            return None

    def _extract_task_name(self, parts: List[str], keyword_index: int) -> Optional[str]:
        """Extract task name, skipping qualifiers."""
        try:
            # Task syntax: [qualifiers] task name(...)
            qualifiers = {'automatic', 'static'}

            i = keyword_index + 1
            while i < len(parts):
                part = parts[i].split('(')[0].split(';')[0].strip()

                # Skip qualifiers
                if part in qualifiers:
                    i += 1
                    continue

                # This should be the task name
                if part.isidentifier():
                    return part

                i += 1

            return None
        except Exception as e:
            logger.debug(f"Error extracting task name: {e}")
            return None

    def _extract_variable_names_from_node(self, node) -> List[str]:
        """Extract variable names from a data declaration node."""
        try:
            text = str(node).strip()
            # Simple parsing for variable declarations
            if any(keyword in text for keyword in ['logic', 'reg', 'wire', 'int', 'bit']):
                words = text.replace(';', '').split()
                names = []
                for word in words:
                    clean_word = word.strip(',();')
                    if (clean_word.isidentifier() and
                        clean_word not in ['logic', 'reg', 'wire', 'int', 'bit', 'input', 'output', 'inout']):
                        names.append(clean_word)
                return names
            return []
        except Exception as e:
            logger.debug(f"Error extracting variable names: {e}")
            return []

    def _extract_port_name_from_node(self, node) -> Optional[str]:
        """Extract port name from a port declaration node."""
        try:
            text = str(node).strip()
            words = text.split()
            for word in reversed(words):
                clean_word = word.strip(',();')
                if clean_word.isidentifier() and clean_word not in ['input', 'output', 'inout', 'logic', 'wire']:
                    return clean_word
            return None
        except Exception as e:
            logger.debug(f"Error extracting port name: {e}")
            return None


    # Placeholder methods for other symbol types (to be implemented as needed)
    def _create_program_symbol(self, node, location): return None
    def _create_package_symbol(self, node, location): return None
    def _create_task_symbol(self, node, location): return None
    def _create_net_symbol(self, node, location): return None
    def _create_parameter_symbol(self, node, location): return None
    def _create_localparam_symbol(self, node, location): return None
    def _create_explicit_port_symbol(self, node, location): return None
    def _create_typedef_symbol(self, node, location): return None
    def _create_enum_symbol(self, node, location): return None
    def _create_struct_symbol(self, node, location): return None
    def _create_union_symbol(self, node, location): return None
    def _create_generate_block_symbol(self, node, location): return None
    def _create_generate_loop_symbol(self, node, location): return None
    def _create_always_block_symbol(self, node, location): return None
    def _create_initial_block_symbol(self, node, location): return None
    def _create_final_block_symbol(self, node, location): return None
    def _create_property_symbol(self, node, location): return None
    def _create_sequence_symbol(self, node, location): return None
    def _create_covergroup_symbol(self, node, location): return None
    def _create_clocking_symbol(self, node, location): return None
    def _create_modport_symbol(self, node, location): return None
    def _create_instance_symbol(self, node, location): return None
