"""
SystemVerilog Language Server implementation.

This module provides the main LSP server class that handles client communication
and coordinates between different components.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional
from io import StringIO

from ..core.symbol_manager import SymbolManager
from ..core.position_mapper import <PERSON>si<PERSON><PERSON>apper
from ..core.workspace import Workspace<PERSON>anager
from ..core.cache import CacheManager
from .protocol import LSPProtocol
from .handlers import LSPHandlers
from .enhanced_handlers import EnhancedLSPHandlers

logger = logging.getLogger(__name__)


class SVLanguageServer:
    """
    Main SystemVerilog Language Server class.

    This class coordinates between different components and handles
    the LSP protocol communication with clients.
    """

    def __init__(self, max_workers: int = 4, use_enhanced_handlers: bool = True):
        """
        Initialize the language server.

        Args:
            max_workers: Maximum number of worker threads
            use_enhanced_handlers: Whether to use enhanced LSP handlers with semantic model
        """
        self.max_workers = max_workers
        self.is_running = False
        self.use_enhanced_handlers = use_enhanced_handlers

        # Initialize components
        self.symbol_manager = SymbolManager()
        self.position_mapper = PositionMapper()
        self.workspace_manager = WorkspaceManager()
        self.cache_manager = CacheManager()
        self.protocol = LSPProtocol()

        # Choose handler implementation based on configuration
        if use_enhanced_handlers:
            self.handlers = EnhancedLSPHandlers(
                symbol_manager=self.symbol_manager,
                position_mapper=self.position_mapper,
                workspace_manager=self.workspace_manager
            )
            logger.info("Using enhanced LSP handlers with semantic model")
        else:
            self.handlers = LSPHandlers(
                symbol_manager=self.symbol_manager,
                position_mapper=self.position_mapper,
                workspace_manager=self.workspace_manager
            )
            logger.info("Using basic LSP handlers")

        # Message handling
        self.request_handlers = {
            "initialize": self._handle_initialize,
            "textDocument/definition": self._handle_goto_definition,
            "textDocument/references": self._handle_find_references,
            "textDocument/hover": self._handle_hover,
            "textDocument/documentSymbol": self._handle_document_symbol,
            "workspace/symbol": self._handle_workspace_symbol,
            "shutdown": self._handle_shutdown,
        }

        self.notification_handlers = {
            "initialized": self._handle_initialized,
            "textDocument/didOpen": self._handle_did_open,
            "textDocument/didChange": self._handle_did_change,
            "textDocument/didSave": self._handle_did_save,
            "textDocument/didClose": self._handle_did_close,
            "workspace/didChangeConfiguration": self._handle_did_change_configuration,
            "workspace/didChangeWatchedFiles": self._handle_did_change_watched_files,
            "exit": self._handle_exit,
        }

        logger.info("SystemVerilog Language Server initialized")

    async def start_io(self) -> None:
        """
        Start the server using stdin/stdout for communication.

        This is the standard mode for LSP servers.
        """
        logger.info("Starting LSP server on stdin/stdout")
        self.is_running = True

        try:
            # Create reader for stdin
            reader = asyncio.StreamReader()
            protocol = asyncio.StreamReaderProtocol(reader)
            await asyncio.get_event_loop().connect_read_pipe(lambda: protocol, sys.stdin)

            # Create a simple writer wrapper for stdout
            class StdoutWriter:
                def write(self, data):
                    sys.stdout.buffer.write(data)
                    sys.stdout.flush()

                async def drain(self):
                    pass

                def close(self):
                    pass

                async def wait_closed(self):
                    pass

            writer = StdoutWriter()

            await self._handle_client(reader, writer)

        except KeyboardInterrupt:
            logger.info("Server interrupted")
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            await self.shutdown()

    async def start_tcp(self, host: str, port: int) -> None:
        """
        Start the server using TCP for communication.

        Args:
            host: Host to bind to
            port: Port to bind to
        """
        logger.info(f"Starting LSP server on TCP {host}:{port}")
        self.is_running = True

        try:
            server = await asyncio.start_server(
                self._handle_client, host, port
            )

            async with server:
                await server.serve_forever()

        except KeyboardInterrupt:
            logger.info("Server interrupted")
        except Exception as e:
            logger.error(f"TCP server error: {e}")
        finally:
            await self.shutdown()

    async def _handle_client(self, reader: asyncio.StreamReader, writer) -> None:
        """
        Handle client communication.

        Args:
            reader: Stream reader for incoming messages
            writer: Stream writer for outgoing messages
        """
        logger.info("Client connected")

        try:
            while self.is_running:
                # Read message
                message = await self._read_message(reader)
                if message is None:
                    break

                # Process message
                response = await self._process_message(message)

                # Send response if needed
                if response:
                    await self._write_message(writer, response)

        except Exception as e:
            logger.error(f"Error handling client: {e}")
        finally:
            writer.close()
            await writer.wait_closed()
            logger.info("Client disconnected")

    async def _read_message(self, reader: asyncio.StreamReader) -> Optional[Dict[str, Any]]:
        """
        Read a JSON-RPC message from the stream.

        Args:
            reader: Stream reader

        Returns:
            Parsed message or None if connection closed
        """
        try:
            # Read headers
            headers = {}
            while True:
                line = await reader.readline()
                if not line:
                    return None

                line = line.decode('utf-8').strip()
                if not line:
                    break

                key, value = line.split(':', 1)
                headers[key.strip()] = value.strip()

            # Read content
            content_length = int(headers.get('Content-Length', 0))
            if content_length == 0:
                return None

            content = await reader.read(content_length)
            if not content:
                return None

            # Parse JSON
            message = json.loads(content.decode('utf-8'))
            logger.debug(f"Received message: {message.get('method', 'response')}")
            return message

        except Exception as e:
            logger.error(f"Error reading message: {e}")
            return None

    async def _write_message(self, writer: asyncio.StreamWriter, message: Dict[str, Any]) -> None:
        """
        Write a JSON-RPC message to the stream.

        Args:
            writer: Stream writer
            message: Message to send
        """
        try:
            content = json.dumps(message, separators=(',', ':'))
            content_bytes = content.encode('utf-8')

            # Write headers
            headers = f"Content-Length: {len(content_bytes)}\r\n\r\n"
            writer.write(headers.encode('utf-8'))

            # Write content
            writer.write(content_bytes)
            await writer.drain()

            logger.debug(f"Sent message: {message.get('method', 'response')}")

        except Exception as e:
            logger.error(f"Error writing message: {e}")

    async def shutdown(self) -> None:
        """Shutdown the server gracefully."""
        logger.info("Shutting down LSP server")
        self.is_running = False

        # Cleanup resources
        self.symbol_manager.clear()
        self.position_mapper.clear()
        self.workspace_manager.clear()
        self.cache_manager.clear_all()

    async def _process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process an incoming LSP message.

        Args:
            message: Incoming message

        Returns:
            Response message or None for notifications
        """
        try:
            method = message.get("method")
            params = message.get("params", {})
            message_id = message.get("id")

            # Handle requests
            if message_id is not None:
                if method in self.request_handlers:
                    result = await self.request_handlers[method](params)
                    return self.protocol.create_response(message_id, result)
                else:
                    return self.protocol.create_error_response(
                        message_id, -32601, f"Method not found: {method}"
                    )

            # Handle notifications
            else:
                if method in self.notification_handlers:
                    await self.notification_handlers[method](params)
                else:
                    logger.warning(f"Unknown notification: {method}")
                return None

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            if message.get("id") is not None:
                return self.protocol.create_error_response(
                    message.get("id"), -32603, f"Internal error: {e}"
                )
            return None

    # Request handlers
    async def _handle_initialize(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle initialize request."""
        return self.protocol.handle_initialize(params)

    async def _handle_goto_definition(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle go to definition request."""
        return self.handlers.handle_goto_definition(params)

    async def _handle_find_references(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Handle find references request."""
        return self.handlers.handle_find_references(params)

    async def _handle_hover(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle hover request."""
        return self.handlers.handle_hover(params)

    async def _handle_document_symbol(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Handle document symbol request."""
        return self.handlers.handle_document_symbol(params)

    async def _handle_workspace_symbol(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Handle workspace symbol request."""
        return self.handlers.handle_workspace_symbol(params)

    async def _handle_shutdown(self, params: Dict[str, Any]) -> None:
        """Handle shutdown request."""
        self.protocol.handle_shutdown(params)
        return None

    # Notification handlers
    async def _handle_initialized(self, params: Dict[str, Any]) -> None:
        """Handle initialized notification."""
        self.protocol.handle_initialized(params)

    async def _handle_did_open(self, params: Dict[str, Any]) -> None:
        """Handle textDocument/didOpen notification."""
        self.handlers.handle_text_document_did_open(params)

    async def _handle_did_change(self, params: Dict[str, Any]) -> None:
        """Handle textDocument/didChange notification."""
        self.handlers.handle_text_document_did_change(params)

    async def _handle_did_save(self, params: Dict[str, Any]) -> None:
        """Handle textDocument/didSave notification."""
        self.handlers.handle_text_document_did_save(params)

    async def _handle_did_close(self, params: Dict[str, Any]) -> None:
        """Handle textDocument/didClose notification."""
        self.handlers.handle_text_document_did_close(params)

    async def _handle_did_change_configuration(self, params: Dict[str, Any]) -> None:
        """Handle workspace/didChangeConfiguration notification."""
        self.handlers.handle_workspace_did_change_configuration(params)

    async def _handle_did_change_watched_files(self, params: Dict[str, Any]) -> None:
        """Handle workspace/didChangeWatchedFiles notification."""
        self.handlers.handle_workspace_did_change_watched_files(params)

    async def _handle_exit(self, params: Dict[str, Any]) -> None:
        """Handle exit notification."""
        self.protocol.handle_exit(params)
        await self.shutdown()

    def get_capabilities(self) -> dict:
        """
        Get the server capabilities.

        Returns:
            Dictionary of LSP server capabilities
        """
        return {
            "textDocumentSync": {
                "openClose": True,
                "change": 1,  # Full document sync
                "save": {"includeText": True}
            },
            "definitionProvider": True,
            "referencesProvider": True,
            "hoverProvider": True,
            "documentSymbolProvider": True,
            "workspaceSymbolProvider": True,
        }
