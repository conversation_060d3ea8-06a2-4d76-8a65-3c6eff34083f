#include <iostream>
#include <string>
#include <cassert>

#include "SyntaxToSymbolMapper.h"
#include "SymbolResolver.h"
#include "MappingCache.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"

using namespace syntax_symbol_mapper;
using namespace slang;

void test_comprehensive_mapping() {
    std::cout << "=== Comprehensive Mapping Test ===" << std::endl;
    
    // Create a more complex SystemVerilog source
    std::string source = R"(
package test_pkg;
    typedef struct {
        int x, y;
    } point_t;
    
    parameter int MAX_SIZE = 100;
endpackage

module top_module;
    import test_pkg::*;
    
    // Variable declarations
    int counter;
    logic [7:0] data;
    point_t my_point;
    
    // Parameter
    parameter int WIDTH = 8;
    
    // Submodule instantiation
    sub_module #(.PARAM(WIDTH)) sub_inst (
        .clk(clk),
        .data(data)
    );
    
    // Function declaration
    function automatic int add_points(point_t p1, point_t p2);
        return (p1.x + p1.y) + (p2.x + p2.y);
    endfunction
    
    // Always block
    always_ff @(posedge clk) begin
        counter <= counter + 1;
        my_point.x <= counter;
        my_point.y <= counter * 2;
    end
    
endmodule

module sub_module #(parameter PARAM = 1) (
    input logic clk,
    input logic [7:0] data
);
    logic internal_signal;
    
    always_comb begin
        internal_signal = |data;
    end
endmodule
)";

    // Parse and compile
    auto tree = syntax::SyntaxTree::fromText(source);
    assert(tree != nullptr);
    
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    
    // Force elaboration
    auto diagnostics = compilation.getAllDiagnostics();
    std::cout << "  Compilation diagnostics: " << diagnostics.size() << " issues" << std::endl;
    
    // Create mapper with comprehensive configuration
    MapperConfig config;
    config.enable_caching = true;
    config.deep_traversal = true;
    config.include_implicit_symbols = true;
    config.max_cache_size = 1000;
    
    SyntaxToSymbolMapper mapper(compilation, config);
    
    // Build mapping
    auto result = mapper.buildMapping(*tree);
    assert(result.success);
    std::cout << "  ✓ Mapped " << result.mapped_count << " syntax-symbol pairs" << std::endl;
    
    // Test mapping statistics
    assert(mapper.getMappingCount() > 0);
    std::cout << "  ✓ Total mappings: " << mapper.getMappingCount() << std::endl;
    
    // Test configuration access
    const auto& currentConfig = mapper.getConfig();
    assert(currentConfig.enable_caching == true);
    assert(currentConfig.deep_traversal == true);
    assert(currentConfig.max_cache_size == 1000);
    std::cout << "  ✓ Configuration verified" << std::endl;
    
    // Test symbol lookup
    const auto& syntaxToSymbol = mapper.getSyntaxToSymbolMap();
    const auto& symbolToSyntax = mapper.getSymbolToSyntaxMap();
    
    std::cout << "  ✓ Forward mappings: " << syntaxToSymbol.size() << std::endl;
    std::cout << "  ✓ Reverse mappings: " << symbolToSyntax.size() << std::endl;
    
    // Test bidirectional mapping consistency
    for (const auto& [syntax, symbol] : syntaxToSymbol) {
        auto reverseSyntax = mapper.findSyntax(symbol);
        assert(reverseSyntax == syntax);
    }
    std::cout << "  ✓ Bidirectional mapping consistency verified" << std::endl;
    
    // Test symbol info retrieval
    int infoCount = 0;
    for (const auto& [syntax, symbol] : syntaxToSymbol) {
        auto info = mapper.getSymbolInfo(syntax);
        assert(info.syntax == syntax);
        assert(info.symbol == symbol);
        infoCount++;
        if (infoCount >= 3) break; // Test first few
    }
    std::cout << "  ✓ Symbol info retrieval verified" << std::endl;
}

void test_cache_functionality() {
    std::cout << "\n=== Cache Functionality Test ===" << std::endl;
    
    // Test cache directly
    MappingCache cache(5); // Small cache for testing
    
    // Create dummy pointers for testing
    const SyntaxNode* syntax1 = reinterpret_cast<const SyntaxNode*>(0x1000);
    const SyntaxNode* syntax2 = reinterpret_cast<const SyntaxNode*>(0x2000);
    const Symbol* symbol1 = reinterpret_cast<const Symbol*>(0x1001);
    const Symbol* symbol2 = reinterpret_cast<const Symbol*>(0x2001);
    
    // Test basic cache operations
    assert(cache.size() == 0);
    assert(!cache.contains(syntax1));
    assert(!cache.contains(symbol1));
    
    cache.addMapping(syntax1, symbol1);
    assert(cache.size() == 1);
    assert(cache.contains(syntax1));
    assert(cache.contains(symbol1));
    assert(cache.findSymbol(syntax1) == symbol1);
    assert(cache.findSyntax(symbol1) == syntax1);
    
    cache.addMapping(syntax2, symbol2);
    assert(cache.size() == 2);
    
    // Test cache statistics
    auto stats = cache.getStats();
    assert(stats.current_size == 2);
    assert(stats.max_size == 5);
    std::cout << "  ✓ Cache hit rate: " << (stats.hit_rate * 100) << "%" << std::endl;
    
    // Test cache clear
    cache.clear();
    assert(cache.size() == 0);
    assert(!cache.contains(syntax1));
    
    std::cout << "  ✓ Cache functionality verified" << std::endl;
}

void test_resolver_functionality() {
    std::cout << "\n=== Resolver Functionality Test ===" << std::endl;
    
    std::string source = R"(
module resolver_test;
    int my_var;
    logic [7:0] my_array [0:15];
    
    function int my_func(int param);
        return param * 2;
    endfunction
    
    always @* begin
        my_var = my_func(42);
    end
endmodule
)";

    auto tree = syntax::SyntaxTree::fromText(source);
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    auto diagnostics = compilation.getAllDiagnostics();
    
    SymbolResolver resolver(compilation);
    
    // Test basic resolver functionality
    auto& root = tree->root();
    
    // Test scope finding
    auto rootScope = resolver.findContainingScope(&root);
    assert(rootScope != nullptr);
    std::cout << "  ✓ Root scope found" << std::endl;
    
    // Test symbol declaration detection
    assert(!resolver.isSymbolDeclaration(&root)); // Root is not a declaration
    std::cout << "  ✓ Symbol declaration detection works" << std::endl;
    
    // Test identifier reference detection
    assert(!resolver.isIdentifierReference(&root)); // Root is not an identifier
    std::cout << "  ✓ Identifier reference detection works" << std::endl;
    
    std::cout << "  ✓ Resolver functionality verified" << std::endl;
}

void test_error_handling() {
    std::cout << "\n=== Error Handling Test ===" << std::endl;
    
    // Test with invalid source
    std::string invalidSource = "invalid SystemVerilog syntax !!!";
    auto tree = syntax::SyntaxTree::fromText(invalidSource);
    
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    
    SyntaxToSymbolMapper mapper(compilation);
    auto result = mapper.buildMapping(*tree);
    
    // Should still succeed even with syntax errors
    assert(result.success);
    std::cout << "  ✓ Handles invalid syntax gracefully" << std::endl;
    
    // Test null pointer handling
    assert(mapper.findSymbol(nullptr) == nullptr);
    assert(mapper.findSyntax(nullptr) == nullptr);
    auto info = mapper.getSymbolInfo(nullptr);
    assert(info.syntax == nullptr);
    assert(info.symbol == nullptr);
    std::cout << "  ✓ Null pointer handling verified" << std::endl;
}

int main() {
    std::cout << "Running Comprehensive SyntaxToSymbolMapper Tests" << std::endl;
    std::cout << "================================================" << std::endl;
    
    try {
        test_comprehensive_mapping();
        test_cache_functionality();
        test_resolver_functionality();
        test_error_handling();
        
        std::cout << "\n✓ All comprehensive tests passed!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "\n✗ Test failed: " << e.what() << std::endl;
        return 1;
    }
}
