"""
Tests for SymbolAnalyzer.
"""

import pytest
import sys
from pathlib import Path

# Import test helpers (which handles path setup)
sys.path.insert(0, str(Path(__file__).parent.parent))
from test_helpers import (
    ensure_imports, skip_if_no_imports, requires_pyslang,
    create_test_analyzer, get_sample_systemverilog_code,
    get_complex_systemverilog_code
)

# Import sv_lsp modules (after path setup)
if ensure_imports():
    from sv_lsp.analysis.symbol_analyzer import SymbolAnalyzer
    from sv_lsp.core.types import SymbolKind, Location


class TestSymbolAnalyzer:
    """Test cases for SymbolAnalyzer."""

    @skip_if_no_imports
    def setup_method(self):
        """Set up test fixtures."""
        self.analyzer = create_test_analyzer()

    @skip_if_no_imports
    def test_initialization(self):
        """Test analyzer initialization."""
        analyzer = create_test_analyzer()
        assert analyzer is not None
        assert analyzer.semantic_model is not None
        assert len(analyzer.symbol_stack) == 0
        assert analyzer.current_scope is None

    def test_simple_module_analysis(self):
        """Test analysis of a simple module."""
        code = """
        module simple_test(
            input logic clk,
            output logic data
        );
            logic internal_signal;
        endmodule
        """

        symbols = self.analyzer.analyze_text(code, "test.sv")

        # Should find at least the module and ports
        assert len(symbols) > 0

        # Check for module symbol
        module_symbols = [s for s in symbols if s.kind == SymbolKind.INSTANCE]
        assert len(module_symbols) >= 1

        # Check for port symbols
        port_symbols = [s for s in symbols if s.kind == SymbolKind.PORT]
        assert len(port_symbols) >= 2  # clk and data

    def test_complex_module_analysis(self):
        """Test analysis of a complex module with various constructs."""
        code = """
        module complex_test #(
            parameter int WIDTH = 8
        ) (
            input logic clk,
            input logic rst_n,
            output logic [WIDTH-1:0] result
        );

            logic [WIDTH-1:0] counter;

            function automatic logic [WIDTH-1:0] increment(
                input logic [WIDTH-1:0] value
            );
                return value + 1;
            endfunction

            always_ff @(posedge clk) begin
                if (!rst_n)
                    counter <= 0;
                else
                    counter <= increment(counter);
            end

            assign result = counter;

        endmodule
        """

        symbols = self.analyzer.analyze_text(code, "complex_test.sv")

        # Should find multiple symbol types
        assert len(symbols) > 5

        # Check for different symbol kinds
        symbol_kinds = {s.kind for s in symbols}
        expected_kinds = {SymbolKind.INSTANCE, SymbolKind.PORT, SymbolKind.VARIABLE, SymbolKind.SUBROUTINE}
        assert expected_kinds.issubset(symbol_kinds)

    def test_class_analysis(self):
        """Test analysis of class declarations."""
        code = """
        class test_class;
            rand bit [31:0] data;

            function new();
                data = 0;
            endfunction

            virtual task execute();
                $display("Executing");
            endtask
        endclass
        """

        symbols = self.analyzer.analyze_text(code, "class_test.sv")

        # Should find class and its members
        class_symbols = [s for s in symbols if s.kind == SymbolKind.CLASS_TYPE]
        assert len(class_symbols) >= 1

        # Should find functions/tasks
        subroutine_symbols = [s for s in symbols if s.kind == SymbolKind.SUBROUTINE]
        assert len(subroutine_symbols) >= 1

    def test_interface_analysis(self):
        """Test analysis of interface declarations."""
        code = """
        interface test_if(input logic clk);
            logic [7:0] data;
            logic valid;

            modport master (output data, valid);
            modport slave (input data, valid);
        endinterface
        """

        symbols = self.analyzer.analyze_text(code, "interface_test.sv")

        # Should find interface and signals
        interface_symbols = [s for s in symbols if s.kind == SymbolKind.INSTANCE]
        assert len(interface_symbols) >= 1

        # Should find variables
        variable_symbols = [s for s in symbols if s.kind == SymbolKind.VARIABLE]
        assert len(variable_symbols) >= 2  # data and valid

    def test_semantic_model_integration(self):
        """Test semantic model integration."""
        code = """
        module parent_module;
            logic signal1;

            child_module child_inst();
        endmodule

        module child_module;
            logic signal2;
        endmodule
        """

        symbols = self.analyzer.analyze_text(code, "hierarchy_test.sv")
        semantic_model = self.analyzer.get_semantic_model()

        # Check semantic model has data
        stats = semantic_model.get_statistics()
        assert stats["symbol_locations"] > 0
        assert stats["hierarchical_relationships"] >= 0

    def test_statistics_collection(self):
        """Test statistics collection."""
        code = """
        module stats_test;
            logic [7:0] data;
            logic valid;
        endmodule
        """

        symbols = self.analyzer.analyze_text(code, "stats_test.sv")
        stats = self.analyzer.get_statistics()

        # Check statistics structure
        assert "total_symbols" in stats
        assert "symbols_by_kind" in stats
        assert "analyzer_type" in stats
        assert stats["analyzer_type"] == "complete"
        assert stats["total_symbols"] == len(symbols)

    def test_empty_input(self):
        """Test handling of empty input."""
        symbols = self.analyzer.analyze_text("", "empty.sv")
        assert len(symbols) == 0

    def test_invalid_syntax(self):
        """Test handling of invalid syntax."""
        code = "module invalid syntax here"

        # Should not crash, might return empty or partial results
        symbols = self.analyzer.analyze_text(code, "invalid.sv")
        assert isinstance(symbols, list)

    def test_file_analysis(self, tmp_path):
        """Test file-based analysis."""
        # Create a temporary file
        test_file = tmp_path / "test_module.sv"
        test_file.write_text("""
        module file_test;
            logic signal;
        endmodule
        """)

        symbols = self.analyzer.analyze_file(str(test_file))
        assert len(symbols) > 0

        # Check that file path is correctly set
        for symbol in symbols:
            assert symbol.definition_location.file_path == str(test_file)


class TestSymbolAnalyzerEdgeCases:
    """Test edge cases for SymbolAnalyzer."""

    def setup_method(self):
        """Set up test fixtures."""
        self.analyzer = SymbolAnalyzer()

    def test_nested_modules(self):
        """Test nested module structures."""
        code = """
        module outer_module;
            inner_module inner_inst();
        endmodule

        module inner_module;
            logic nested_signal;
        endmodule
        """

        symbols = self.analyzer.analyze_text(code, "nested.sv")

        # Should handle nested structures
        module_symbols = [s for s in symbols if s.kind == SymbolKind.INSTANCE]
        assert len(module_symbols) >= 2

    def test_generate_blocks(self):
        """Test generate block analysis."""
        code = """
        module generate_test;
            genvar i;
            generate
                for (i = 0; i < 4; i++) begin : gen_block
                    logic [7:0] gen_signal;
                end
            endgenerate
        endmodule
        """

        symbols = self.analyzer.analyze_text(code, "generate.sv")

        # Should find the module at minimum
        assert len(symbols) > 0

    def test_package_analysis(self):
        """Test package analysis."""
        code = """
        package test_pkg;
            typedef enum logic [1:0] {
                STATE_IDLE,
                STATE_ACTIVE
            } state_t;

            parameter int DATA_WIDTH = 32;
        endpackage
        """

        symbols = self.analyzer.analyze_text(code, "package.sv")

        # Should find package elements
        assert len(symbols) >= 0  # Package analysis might be limited

    def test_multiple_modules(self):
        """Test multiple modules in one file."""
        code = """
        module module1;
            logic signal1;
        endmodule

        module module2;
            logic signal2;
        endmodule

        module module3;
            logic signal3;
        endmodule
        """

        symbols = self.analyzer.analyze_text(code, "multiple.sv")

        # Should find all modules
        module_symbols = [s for s in symbols if s.kind == SymbolKind.INSTANCE]
        assert len(module_symbols) >= 3


if __name__ == "__main__":
    pytest.main([__file__])
