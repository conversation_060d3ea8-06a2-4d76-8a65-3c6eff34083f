import pyslang

class SymbolMapper:
    """
    Implements the user's proposed algorithm to map every identifier
    in a syntax tree to its corresponding semantic symbol.
    """
    def __init__(self, compilation):
        self.compilation = compilation
        self.root_scope = compilation.getRoot()
        self.mapping = {}

    def map_symbols(self, tree):
        """
        Traverses the syntax tree and populates the mapping of
        SyntaxNode -> Symbol.
        """
        self._traverse_and_map(tree.root)
        return self.mapping

    def _traverse_and_map(self, node):
        """
        Recursively traverses the tree, resolving symbols for identifiers.
        """
        if node is None:
            return

        if isinstance(node, pyslang.IdentifierNameSyntax):
            resolved_symbol = self._resolve_symbol(node)
            if resolved_symbol:
                self.mapping[node] = resolved_symbol

        if isinstance(node, pyslang.SyntaxNode):
            for child in node:
                self._traverse_and_map(child)

    def _resolve_symbol(self, node_to_resolve):
        """
        Travels up the syntax tree from the given node to find the
        correct semantic scope and resolve the symbol.
        """
        identifier_name = node_to_resolve.identifier.valueText
        current_parent = node_to_resolve.parent

        while current_parent:
            parent_symbol = self._get_symbol_for_syntax(current_parent)

            if parent_symbol and isinstance(parent_symbol, pyslang.Scope):
                found_symbol = parent_symbol.lookupName(identifier_name, pyslang.LookupLocation.max)
                if found_symbol:
                    return found_symbol
            
            current_parent = current_parent.parent
        
        return None

    def _get_symbol_for_syntax(self, syntax_node):
        """
        Finds the semantic Symbol that was created from a specific SyntaxNode.
        """
        if hasattr(syntax_node, 'name') and hasattr(syntax_node.name, 'valueText'):
            path = self._get_qualified_name(syntax_node)
            return self.root_scope.lookupName(path)
        return None

    def _get_qualified_name(self, syntax_node):
        """Creates a hierarchical name for a named syntax node."""
        name_parts = []
        current = syntax_node
        while current:
            if hasattr(current, 'name') and hasattr(current.name, 'valueText'):
                name_parts.append(current.name.valueText)
            
            if isinstance(current, pyslang.ModuleDeclarationSyntax):
                break
            current = current.parent
        return ".".join(reversed(name_parts))


# --- Main ---

source_code = """
module my_mod;
    int i; // The DECLARATION
    always @*
        i <= 1; // The USAGE
endmodule
"""

# 1. Compile for semantics
tree = pyslang.SyntaxTree.fromText(source_code)
compilation = pyslang.Compilation()
compilation.addSyntaxTree(tree)
list(compilation.getAllDiagnostics())

# 2. Use the mapper to build the map
mapper = SymbolMapper(compilation)
symbol_map = mapper.map_symbols(tree)

# 3. Verification
print("--- Verifying Symbol Mapping ---")
# Corrected traversal logic
module_decl_node = tree.root.members[0]
procedural_block_node = module_decl_node.members[1]
usage_node = procedural_block_node.statement.expr.left

# Find the usage node in our map
usage_key_node = None
for node in symbol_map.keys():
    if node.sourceRange == usage_node.sourceRange:
        usage_key_node = node
        break

# Get the declaration symbol directly for comparison
declaration_symbol = compilation.getRoot().lookupName("my_mod.i")

if usage_key_node and declaration_symbol:
    usage_symbol = symbol_map[usage_key_node]
    
    print(f"Symbol for Declaration ('i'): '{declaration_symbol.name}' at {declaration_symbol.location}")
    print(f"Symbol for Usage ('i'):       '{usage_symbol.name}' at {usage_symbol.location}")
    print("-" * 40)

    are_same_object = declaration_symbol is usage_symbol
    print(f"Are they the same symbol object? -> {are_same_object}")
    assert are_same_object
    print("Success! Your algorithm correctly unified the declaration and usage to the same Symbol.")
else:
    print("Verification failed. Could not find the usage node in the map.")