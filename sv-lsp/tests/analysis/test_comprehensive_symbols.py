#!/usr/bin/env python3
"""
Test script for comprehensive SymbolKind support.
"""

import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

try:
    from sv_lsp.core.types import SymbolKind
    from sv_lsp.core.symbol_kind_mapper import SymbolKindMapper
    from sv_lsp.analysis.symbol_analyzer import SymbolAnalyzer

    print("🎯 Comprehensive SymbolKind Support Test")
    print("=" * 60)

    # Test 1: SymbolKind completeness
    print("📊 SymbolKind Completeness Analysis:")
    all_kinds = list(SymbolKind)
    print(f"  Total SymbolKind values: {len(all_kinds)}")

    # Group by category
    categories = {}
    for kind in all_kinds:
        category = SymbolKindMapper.get_category(kind)
        if category not in categories:
            categories[category] = []
        categories[category].append(kind)

    for category, kinds in categories.items():
        print(f"  {category.title()}: {len(kinds)} kinds")
        for kind in kinds[:3]:  # Show first 3 examples
            print(f"    - {kind.value}")
        if len(kinds) > 3:
            print(f"    ... and {len(kinds) - 3} more")
    print()

    # Test 2: LSP mapping
    print("🔗 LSP Protocol Mapping Test:")
    lsp_mappings = {}
    for kind in all_kinds:
        lsp_kind = SymbolKindMapper.to_lsp(kind)
        if lsp_kind not in lsp_mappings:
            lsp_mappings[lsp_kind] = []
        lsp_mappings[lsp_kind].append(kind)

    print(f"  Mapped to {len(lsp_mappings)} LSP SymbolKind values:")
    for lsp_kind, kinds in sorted(lsp_mappings.items()):
        print(f"    LSP {lsp_kind}: {len(kinds)} internal kinds")
    print()

    # Test 3: Container detection
    print("📦 Container Symbol Detection:")
    container_kinds = [kind for kind in all_kinds if SymbolKindMapper.is_container(kind)]
    non_container_kinds = [kind for kind in all_kinds if not SymbolKindMapper.is_container(kind)]

    print(f"  Container symbols: {len(container_kinds)}")
    print(f"  Leaf symbols: {len(non_container_kinds)}")
    print(f"  Container examples: {', '.join(k.value for k in container_kinds[:5])}")
    print()

    # Test 4: Comprehensive analysis with complex SystemVerilog
    print("🔍 Comprehensive Symbol Analysis Test:")

    complex_sv_code = """
// Complex SystemVerilog test case
package test_pkg;
    typedef enum logic [1:0] {
        STATE_IDLE = 2'b00,
        STATE_ACTIVE = 2'b01,
        STATE_DONE = 2'b10
    } state_t;

    typedef struct packed {
        logic [7:0] data;
        logic valid;
        logic ready;
    } bus_t;

    parameter int DATA_WIDTH = 32;
    localparam int ADDR_WIDTH = 16;
endpackage

interface axi_if #(
    parameter int DATA_WIDTH = 32,
    parameter int ADDR_WIDTH = 32
) (
    input logic clk,
    input logic rst_n
);
    logic [ADDR_WIDTH-1:0] awaddr;
    logic awvalid;
    logic awready;

    modport master (
        output awaddr, awvalid,
        input awready
    );

    modport slave (
        input awaddr, awvalid,
        output awready
    );
endinterface

class transaction_base;
    rand bit [31:0] addr;
    rand bit [31:0] data;

    constraint addr_c {
        addr inside {[32'h1000:32'h2000]};
    }

    virtual function void print();
        $display("Transaction: addr=0x%h, data=0x%h", addr, data);
    endfunction

    virtual task execute();
        // Implementation
    endtask
endclass

module cpu_complex #(
    parameter int DATA_WIDTH = 32,
    parameter int CACHE_SIZE = 1024
) (
    input logic clk,
    input logic rst_n,
    axi_if.master mem_if,
    output logic [DATA_WIDTH-1:0] result
);

    // Internal types
    typedef enum logic [2:0] {
        FETCH, DECODE, EXECUTE, WRITEBACK
    } pipeline_stage_t;

    // Internal signals
    logic [DATA_WIDTH-1:0] pc;
    logic [DATA_WIDTH-1:0] instruction;
    pipeline_stage_t current_stage;

    // Memory array
    logic [DATA_WIDTH-1:0] cache [CACHE_SIZE];

    // Functions
    function automatic logic [DATA_WIDTH-1:0] alu_add(
        input logic [DATA_WIDTH-1:0] a,
        input logic [DATA_WIDTH-1:0] b
    );
        return a + b;
    endfunction

    // Tasks
    task automatic fetch_instruction();
        // Fetch logic
        instruction <= cache[pc[15:0]];
    endtask

    // Always blocks
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pc <= 0;
            current_stage <= FETCH;
        end else begin
            case (current_stage)
                FETCH: begin
                    fetch_instruction();
                    current_stage <= DECODE;
                end
                DECODE: begin
                    current_stage <= EXECUTE;
                end
                EXECUTE: begin
                    result <= alu_add(pc, instruction);
                    current_stage <= WRITEBACK;
                end
                WRITEBACK: begin
                    current_stage <= FETCH;
                    pc <= pc + 4;
                end
            endcase
        end
    end

    // Generate blocks
    genvar i;
    generate
        for (i = 0; i < 4; i++) begin : gen_pipeline_regs
            logic [DATA_WIDTH-1:0] pipeline_reg;

            always_ff @(posedge clk) begin
                if (!rst_n)
                    pipeline_reg <= 0;
                else
                    pipeline_reg <= cache[i];
            end
        end
    endgenerate

    // Assertions
    property valid_pc;
        @(posedge clk) disable iff (!rst_n)
        pc < CACHE_SIZE;
    endproperty

    assert property (valid_pc) else $error("PC out of range");

    // Coverage
    covergroup cg_pipeline @(posedge clk);
        stage_cp: coverpoint current_stage;
        pc_cp: coverpoint pc[7:0] {
            bins low = {[0:63]};
            bins mid = {[64:127]};
            bins high = {[128:255]};
        }
        cross_cp: cross stage_cp, pc_cp;
    endgroup

    cg_pipeline cg_inst = new();

endmodule

program test_program;
    initial begin
        $display("Test program started");
        #100;
        $finish;
    end
endprogram
"""

    analyzer = SymbolAnalyzer()
    symbols = analyzer.analyze_text(complex_sv_code, "complex_test.sv")

    print(f"✅ Analyzed complex SystemVerilog code")
    print(f"  Total symbols found: {len(symbols)}")

    # Group symbols by kind
    symbol_groups = {}
    for symbol in symbols:
        kind_name = symbol.kind.value
        if kind_name not in symbol_groups:
            symbol_groups[kind_name] = []
        symbol_groups[kind_name].append(symbol)

    print(f"  Symbol types found: {len(symbol_groups)}")
    for kind_name, group_symbols in sorted(symbol_groups.items()):
        print(f"    {kind_name}: {len(group_symbols)} symbols")
        for sym in group_symbols[:2]:  # Show first 2 examples
            print(f"      - {sym.name}")
        if len(group_symbols) > 2:
            print(f"      ... and {len(group_symbols) - 2} more")
    print()

    # Test 5: Semantic model capabilities
    print("🧠 Semantic Model Capabilities:")
    semantic_model = analyzer.get_semantic_model()
    stats = semantic_model.get_statistics()

    for key, value in stats.items():
        print(f"  {key}: {value}")
    print()

    # Test 6: LSP integration test
    print("🔌 LSP Integration Test:")
    lsp_symbols = []
    for symbol in symbols:
        lsp_kind = SymbolKindMapper.to_lsp(symbol.kind)
        lsp_symbol = {
            "name": symbol.name,
            "kind": lsp_kind,
            "location": {
                "uri": f"file://{symbol.definition_location.file_path}",
                "range": {
                    "start": {
                        "line": symbol.definition_location.line,
                        "character": symbol.definition_location.column
                    },
                    "end": {
                        "line": symbol.definition_location.line,
                        "character": symbol.definition_location.column + len(symbol.name)
                    }
                }
            }
        }
        lsp_symbols.append(lsp_symbol)

    print(f"  Generated {len(lsp_symbols)} LSP symbols")
    print(f"  LSP kinds used: {len(set(s['kind'] for s in lsp_symbols))}")
    print()

    print("🎉 Comprehensive SymbolKind Support Test Complete!")
    print()
    print("Summary:")
    print(f"✅ {len(all_kinds)} SymbolKind values defined")
    print(f"✅ {len(categories)} symbol categories supported")
    print(f"✅ {len(lsp_mappings)} LSP mappings configured")
    print(f"✅ {len(container_kinds)} container types identified")
    print(f"✅ {len(symbols)} symbols extracted from complex code")
    print(f"✅ Full LSP protocol compatibility")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
