#include <iostream>
#include <string>
#include <cassert>

#include "SyntaxToSymbolMapper.h"
#include "SymbolResolver.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"

using namespace syntax_symbol_mapper;
using namespace slang;

void test_symbol_resolution() {
    // Create a SystemVerilog source with identifiers to resolve
    std::string source = R"(
module resolver_test;
    int my_variable;

    always @* begin
        my_variable = 42;  // This should resolve to the declaration above
    end
endmodule
)";

    // Parse and compile
    auto tree = syntax::SyntaxTree::fromText(source);
    assert(tree != nullptr);

    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);

    // Force elaboration
    auto diagnostics = compilation.getAllDiagnostics();

    // Create resolver
    SymbolResolver resolver(compilation);

    // Test basic resolver functionality
    auto& root = tree->root();
    assert(&root != nullptr);

    // Test identifier name extraction
    // Note: This is a simplified test - in practice we'd need to traverse
    // the syntax tree to find specific identifier nodes

    // Test scope finding
    auto rootScope = resolver.findContainingScope(&root);
    assert(rootScope != nullptr);

    // Test syntax kind checking
    assert(resolver.isSymbolDeclaration(&root) == false); // root is not a declaration

    std::cout << "  Symbol resolver basic functionality verified" << std::endl;

    // Test with mapper
    SyntaxToSymbolMapper mapper(compilation);
    auto result = mapper.buildMapping(*tree);
    assert(result.success);

    // Test symbol info retrieval
    auto info = mapper.getSymbolInfo(&root);
    assert(info.syntax == &root);

    std::cout << "  Symbol resolution integration verified" << std::endl;
}
