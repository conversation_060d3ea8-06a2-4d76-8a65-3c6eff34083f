#include <iostream>
#include <string>
#include <cassert>

#include "SyntaxToSymbolMapper.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"

using namespace syntax_symbol_mapper;
using namespace slang;

void test_basic_mapping() {
    // Create a simple SystemVerilog source
    std::string source = R"(
module test_module;
    int variable_a;
    logic [7:0] variable_b;
    
    always @* begin
        variable_a = 1;
        variable_b = 8'h42;
    end
endmodule
)";

    // Parse the source into a syntax tree
    auto tree = syntax::SyntaxTree::fromText(source);
    assert(tree != nullptr);
    
    // Create compilation and add the syntax tree
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    
    // Force elaboration
    auto diagnostics = compilation.getAllDiagnostics();
    
    // Create the mapper
    MapperConfig config;
    config.enable_caching = true;
    config.deep_traversal = true;
    
    SyntaxToSymbolMapper mapper(compilation, config);
    
    // Build the mapping
    auto result = mapper.buildMapping(*tree);
    
    // Check that mapping was successful
    assert(result.success);
    std::cout << "  Mapped " << result.mapped_count << " syntax-symbol pairs" << std::endl;
    
    // Test basic functionality
    assert(mapper.getMappingCount() > 0);
    
    // Test configuration
    assert(mapper.getConfig().enable_caching == true);
    assert(mapper.getConfig().deep_traversal == true);
    
    // Test clear functionality
    auto original_count = mapper.getMappingCount();
    mapper.clear();
    assert(mapper.getMappingCount() == 0);
    
    // Rebuild mapping
    result = mapper.buildMapping(*tree);
    assert(result.success);
    assert(mapper.getMappingCount() > 0);
    
    std::cout << "  Basic mapping functionality verified" << std::endl;
}
