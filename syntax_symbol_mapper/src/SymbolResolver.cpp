#include "SymbolResolver.h"

#include "slang/ast/Compilation.h"
#include "slang/ast/Symbol.h"
#include "slang/ast/Scope.h"
#include "slang/syntax/SyntaxNode.h"
#include "slang/syntax/AllSyntax.h"

namespace syntax_symbol_mapper {

class SymbolResolver::Impl {
public:
    explicit Impl(Compilation& comp) : compilation(comp), rootScope(comp.getRoot().as<Scope>()) {}

    const Symbol* resolveSymbol(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        // First check if this syntax node created a symbol directly
        auto directSymbol = findSymbolFromSyntax(syntax);
        if (directSymbol) return directSymbol;

        // If it's an identifier reference, resolve it
        if (isIdentifierReference(syntax)) {
            return resolveIdentifier(syntax);
        }

        return nullptr;
    }

    const Symbol* resolveIdentifier(const SyntaxNode* identifierNode) {
        if (!identifierNode || !isIdentifierReference(identifierNode)) {
            return nullptr;
        }

        std::string identifierName = getIdentifierName(identifierNode);
        if (identifierName.empty()) return nullptr;

        // Traverse up the syntax tree to find the containing scope
        const SyntaxNode* current = identifierNode->parent;
        while (current) {
            auto scope = getScopeForSyntax(current);
            if (scope) {
                // Look up the identifier in this scope
                auto symbol = scope->lookupName(identifierName);
                if (symbol) return symbol;
            }
            current = current->parent;
        }

        // If not found in any parent scope, try the root scope
        return rootScope.lookupName(identifierName);
    }

    const Symbol* findSymbolFromSyntax(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        // Use the compilation to find symbols that were created from this syntax
        // This leverages slang's internal mapping from syntax to symbols

        // For module declarations
        if (syntax->kind == slang::syntax::SyntaxKind::ModuleDeclaration) {
            auto& moduleDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
            return rootScope.lookupName(moduleDecl.header->name.valueText());
        }

        // For variable declarations
        if (syntax->kind == slang::syntax::SyntaxKind::DataDeclaration) {
            // This is more complex - we need to find the parent scope and look for the variable
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                // Look through the scope's members to find one with matching syntax
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For other syntax types, we can extend this logic
        return nullptr;
    }

    const Scope* getScopeForSyntax(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        // Check if this syntax node represents a scope-creating construct
        switch (syntax->kind) {
            case slang::syntax::SyntaxKind::ModuleDeclaration: {
                auto& moduleDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
                auto symbol = rootScope.lookupName(moduleDecl.header->name.valueText());
                if (symbol && symbol->isScope()) {
                    return &symbol->as<Scope>();
                }
                break;
            }
            case slang::syntax::SyntaxKind::InterfaceDeclaration:
            case slang::syntax::SyntaxKind::ProgramDeclaration:
            case slang::syntax::SyntaxKind::PackageDeclaration:
                // Similar logic for other scope-creating constructs
                break;
            default:
                break;
        }

        return nullptr;
    }

    const Scope* findContainingScope(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        const SyntaxNode* current = syntax->parent;
        while (current) {
            auto scope = getScopeForSyntax(current);
            if (scope) return scope;
            current = current->parent;
        }

        return &rootScope;
    }

    bool isSymbolDeclaration(const SyntaxNode* syntax) {
        if (!syntax) return false;

        switch (syntax->kind) {
            case slang::syntax::SyntaxKind::ModuleDeclaration:
            case slang::syntax::SyntaxKind::InterfaceDeclaration:
            case slang::syntax::SyntaxKind::ProgramDeclaration:
            case slang::syntax::SyntaxKind::PackageDeclaration:
            case slang::syntax::SyntaxKind::DataDeclaration:
            case slang::syntax::SyntaxKind::NetDeclaration:
            case slang::syntax::SyntaxKind::ForVariableDeclaration:
            case slang::syntax::SyntaxKind::ParameterDeclaration:
            case slang::syntax::SyntaxKind::FunctionDeclaration:
            case slang::syntax::SyntaxKind::TaskDeclaration:
                return true;
            default:
                return false;
        }
    }

    bool isIdentifierReference(const SyntaxNode* syntax) {
        if (!syntax) return false;

        switch (syntax->kind) {
            case slang::syntax::SyntaxKind::IdentifierName:
            case slang::syntax::SyntaxKind::IdentifierSelectName:
                return true;
            default:
                return false;
        }
    }

    std::string getIdentifierName(const SyntaxNode* syntax) {
        if (!syntax) return "";

        if (syntax->kind == slang::syntax::SyntaxKind::IdentifierName) {
            auto& identName = syntax->as<slang::syntax::IdentifierNameSyntax>();
            return std::string(identName.identifier.valueText());
        }

        return "";
    }

    std::string getQualifiedName(const SyntaxNode* syntax) {
        if (!syntax) return "";

        std::vector<std::string> nameParts;
        const SyntaxNode* current = syntax;

        while (current) {
            if (current->kind == slang::syntax::SyntaxKind::ModuleDeclaration) {
                auto& moduleDecl = current->as<slang::syntax::ModuleDeclarationSyntax>();
                nameParts.push_back(std::string(moduleDecl.header->name.valueText()));
                break;
            }
            // Add other named constructs as needed
            current = current->parent;
        }

        std::string result;
        for (auto it = nameParts.rbegin(); it != nameParts.rend(); ++it) {
            if (!result.empty()) result += ".";
            result += *it;
        }

        return result;
    }

private:
    Compilation& compilation;
    const Scope& rootScope;
};

// Implementation of public interface
SymbolResolver::SymbolResolver(Compilation& compilation)
    : pImpl(std::make_unique<Impl>(compilation)) {
}

SymbolResolver::~SymbolResolver() = default;

const Symbol* SymbolResolver::resolveSymbol(const SyntaxNode* syntax) {
    return pImpl->resolveSymbol(syntax);
}

const Symbol* SymbolResolver::resolveIdentifier(const SyntaxNode* identifierNode) {
    return pImpl->resolveIdentifier(identifierNode);
}

const Symbol* SymbolResolver::findSymbolFromSyntax(const SyntaxNode* syntax) {
    return pImpl->findSymbolFromSyntax(syntax);
}

const Scope* SymbolResolver::getScopeForSyntax(const SyntaxNode* syntax) {
    return pImpl->getScopeForSyntax(syntax);
}

const Scope* SymbolResolver::findContainingScope(const SyntaxNode* syntax) {
    return pImpl->findContainingScope(syntax);
}

bool SymbolResolver::isSymbolDeclaration(const SyntaxNode* syntax) {
    return pImpl->isSymbolDeclaration(syntax);
}

bool SymbolResolver::isIdentifierReference(const SyntaxNode* syntax) {
    return pImpl->isIdentifierReference(syntax);
}

std::string SymbolResolver::getIdentifierName(const SyntaxNode* syntax) {
    return pImpl->getIdentifierName(syntax);
}

std::string SymbolResolver::getQualifiedName(const SyntaxNode* syntax) {
    return pImpl->getQualifiedName(syntax);
}

} // namespace syntax_symbol_mapper
