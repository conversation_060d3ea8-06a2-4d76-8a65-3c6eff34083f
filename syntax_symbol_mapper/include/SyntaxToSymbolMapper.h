#pragma once

#include "Common.h"
#include <memory>

namespace syntax_symbol_mapper {

/**
 * @brief Main class for mapping SyntaxTree nodes to Symbol objects
 * 
 * This class provides the core functionality to create bidirectional mappings
 * between syntax nodes and semantic symbols using the slang library.
 */
class SyntaxToSymbolMapper {
public:
    /**
     * @brief Constructor
     * @param compilation The slang compilation object containing the semantic model
     * @param config Configuration options for the mapper
     */
    explicit SyntaxToSymbolMapper(Compilation& compilation, 
                                  const MapperConfig& config = MapperConfig{});
    
    /**
     * @brief Destructor
     */
    ~SyntaxToSymbolMapper();
    
    // Disable copy constructor and assignment
    SyntaxToSymbolMapper(const SyntaxToSymbolMapper&) = delete;
    SyntaxToSymbolMapper& operator=(const SyntaxToSymbolMapper&) = delete;
    
    // Enable move constructor and assignment
    SyntaxToSymbolMapper(SyntaxToSymbolMapper&&) noexcept;
    SyntaxToSymbolMapper& operator=(SyntaxToSymbolMapper&&) noexcept;
    
    /**
     * @brief Build complete mapping for a syntax tree
     * @param tree The syntax tree to map
     * @return Result containing success status and statistics
     */
    MappingResult buildMapping(const SyntaxTree& tree);
    
    /**
     * @brief Find symbol for a given syntax node
     * @param syntax The syntax node to look up
     * @return Pointer to the corresponding symbol, or nullptr if not found
     */
    const Symbol* findSymbol(const SyntaxNode* syntax) const;
    
    /**
     * @brief Find syntax node for a given symbol
     * @param symbol The symbol to look up
     * @return Pointer to the corresponding syntax node, or nullptr if not found
     */
    const SyntaxNode* findSyntax(const Symbol* symbol) const;
    
    /**
     * @brief Get detailed information about a symbol
     * @param syntax The syntax node to get info for
     * @return SymbolInfo structure with detailed information
     */
    SymbolInfo getSymbolInfo(const SyntaxNode* syntax) const;
    
    /**
     * @brief Get all mapped syntax-symbol pairs
     * @return Reference to the internal mapping
     */
    const SyntaxToSymbolMap& getSyntaxToSymbolMap() const;
    
    /**
     * @brief Get all mapped symbol-syntax pairs
     * @return Reference to the internal reverse mapping
     */
    const SymbolToSyntaxMap& getSymbolToSyntaxMap() const;
    
    /**
     * @brief Clear all mappings and reset the mapper
     */
    void clear();
    
    /**
     * @brief Get mapping statistics
     * @return Number of currently mapped pairs
     */
    size_t getMappingCount() const;
    
    /**
     * @brief Check if a syntax node has been mapped
     * @param syntax The syntax node to check
     * @return True if the node has a corresponding symbol
     */
    bool isMapped(const SyntaxNode* syntax) const;
    
    /**
     * @brief Update configuration
     * @param config New configuration options
     */
    void updateConfig(const MapperConfig& config);
    
    /**
     * @brief Get current configuration
     * @return Current configuration options
     */
    const MapperConfig& getConfig() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace syntax_symbol_mapper
